#!/bin/bash

# Activate virtual environment if needed (ensure this path is correct)
source .venv/bin/activate

# if [ -f ".venv/bin/activate" ]; then
#   source .venv/bin/activate
# else
#   echo "警告: 未找到虚拟环境激活脚本 '.venv/bin/activate'。"
#   # 如果找不到 venv，可以选择退出或继续
#   # exit 1
# fi

PORT=8100
APP_MODULE="src.app:app"
LOG_FILE="output.log"
# 使用 pgrep -f 匹配包含模块和端口的完整命令行，更可靠
PROCESS_PATTERN="uvicorn ${APP_MODULE}.*--port ${PORT}"

# 1. Find and kill the old process using pgrep
echo "正在查找监听端口 ${PORT} 的旧 uvicorn 进程 (匹配模式: '${PROCESS_PATTERN}')..."
# pgrep -f matches against the full command line argument list
PID=$(pgrep -f "${PROCESS_PATTERN}")

if [ -n "$PID" ]; then
  echo "发现旧的 uvicorn 进程 (PID: $PID)，正在尝试优雅地杀掉..."
  kill $PID
  sleep 2 # Give it a moment to shut down gracefully
  # Check if the process is still running using kill -0 (doesn't send signal, just checks existence)
  if kill -0 $PID 2>/dev/null; then
    echo "进程 ${PID} 未能优雅关闭，正在强制杀掉 (kill -9)..."
    kill -9 $PID
    sleep 1 # Give OS a moment after force kill
  else
    echo "进程 ${PID} 已成功关闭。"
  fi
else
  echo "未发现匹配 '${PROCESS_PATTERN}' 的旧 uvicorn 进程。"
fi

# Fallback: Check port occupation using lsof if pgrep didn't find anything but port is busy
# This requires lsof to be installed (sudo apt install lsof)
if ! pgrep -f "${PROCESS_PATTERN}" > /dev/null && lsof -i :$PORT > /dev/null ; then
    echo "警告：pgrep 未找到进程，但端口 ${PORT} 仍被占用。尝试使用 lsof 查找并强制关闭..."
    # lsof -t outputs only PIDs
    LSOF_PID=$(lsof -t -i :${PORT})
    if [ -n "$LSOF_PID" ]; then
        echo "通过 lsof 找到占用端口 ${PORT} 的进程 (PID: $LSOF_PID)，正在强制杀掉..."
        # It might be multiple PIDs, kill them all
        kill -9 $LSOF_PID
        sleep 1
    else
        echo "lsof 未能找到占用端口 ${PORT} 的进程，情况异常。"
    fi
fi

# 2. Start the new uvicorn service
echo "正在启动新的 uvicorn 服务，监听端口 ${PORT}..."
nohup uvicorn ${APP_MODULE} --host 0.0.0.0 --port ${PORT} > ${LOG_FILE} 2>&1 &
# Store the PID of the background process started by nohup
NOHUP_PID=$!

sleep 2 # Give the server a little time to start up

# 3. Verify the new process started
echo "正在验证新进程 (PID: $NOHUP_PID) 是否成功启动..."
# Check if the process started by nohup is still running
if kill -0 $NOHUP_PID 2>/dev/null; then
  # Optional: double-check using pgrep again to see if it matches the expected pattern
  VERIFY_PID=$(pgrep -f "${PROCESS_PATTERN}")
  # Check if the PID found by pgrep includes the one we just started
  if echo "$VERIFY_PID" | grep -qw "$NOHUP_PID"; then
      echo "启动成功，新的 uvicorn 进程 PID: $NOHUP_PID"
      echo "日志文件: ${LOG_FILE}"
  else
      # This might happen if the process starts but command line doesn't match pattern immediately, or if multiple match
      echo "警告：Nohup 进程 (PID: $NOHUP_PID) 存在，但 pgrep 未能按预期匹配 (${PROCESS_PATTERN})。服务可能仍在启动或存在问题。请检查服务状态和日志 ${LOG_FILE}。"
  fi
else
  echo "启动失败。Nohup 启动的进程 (原 PID: $NOHUP_PID) 未运行或立即退出。请检查日志文件: ${LOG_FILE}"
  # Optionally cat the last few lines of the log
  echo "--- 最近的日志 (${LOG_FILE}) ---"
  tail -n 15 ${LOG_FILE}
  echo "---------------------------"
fi

# Deactivate virtual environment if it was activated
# Check if deactivate command exists and if VIRTUAL_ENV is set
if command -v deactivate &> /dev/null && [[ -n "$VIRTUAL_ENV" ]]; then
  echo "退出虚拟环境..."
  deactivate
fi
