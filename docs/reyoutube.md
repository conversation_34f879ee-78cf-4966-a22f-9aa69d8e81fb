

**YouTube 数据获取服务 - 交互逻辑架构与说明书**

**版本:** 1.0
**日期:** 2023-10-27

**目录**

1.  概述
2.  架构设计
    2.1. 架构图 (逻辑)
    2.2. 核心组件说明
3.  交互逻辑与工作流
    3.1. 核心流程：获取频道/视频公开数据
    3.2. 核心流程：获取评论数据
    3.3. 核心流程：获取用户授权的私密分析数据
    3.4. 任务调度与去重
    3.5. 错误处理与重试
4.  数据模型 (PostgreSQL SQL)
5.  外部 API 交互
    5.1. YouTube Data API v3
    5.2. YouTube Analytics API
    5.3. (可选) 网页抓取注意事项
6.  配置管理
7.  日志与监控
8.  安全性考虑
9.  部署建议
10. 未来扩展

---

**1. 概述**

本服务旨在通过 Python 语言，与 YouTube 提供的 API (以及可能的辅助抓取手段) 进行交互，获取频道、视频、评论及用户授权的分析数据，并将这些数据结构化地存储到 PostgreSQL 数据库中。其目标是为下游的数据分析、展示平台提供稳定、可靠、尽可能全面的数据源，并内置去重、错误处理和调度机制。

**2. 架构设计**

**2.1. 架构图 (逻辑)**

```
+---------------------+      +-----------------+      +---------------------+      +-----------------+      +-------------------+
|  任务触发器         | ---> |  任务队列        | ---> |  数据获取 Worker(s) | ---> |  数据处理/验证   | ---> |  数据库存储       |
| (Scheduler/API)   |      | (e.g., Celery)  |      | (Python Processes)  |      | (Python Logic)  |      | (PostgreSQL)    |
+---------------------+      +-----------------+      +---------------------+      +-----------------+      +-------------------+
      ^                                                     |         |  ^                      ^
      |                                                     | (API)   | (Scraping?)           | (Logging)
      | (User Request)                                      v         v                       |
      |                                             +---------------------+                   |
+---------------------+                             |  YouTube APIs/Web   |                   |
|  外部请求/用户界面  |                             +---------------------+                   |
+---------------------+                                                                       |
                                                                                              v
                                                                                    +---------------------+
                                                                                    |  日志/监控系统       |
                                                                                    +---------------------+
```

**2.2. 核心组件说明**

*   **任务触发器 (Scheduler/API):**
    *   **调度器 (Scheduler):** 使用如 `APScheduler` 或 Celery Beat，负责定时触发周期性任务，例如：每日刷新热门频道、定期检查需要更新的频道/视频、每日获取授权用户的分析数据。
    *   **API 接口 (Optional but Recommended):** 提供一个简单的内部 API (如 Flask/FastAPI)，接收来自其他服务或用户界面的即时请求，例如：用户添加新频道追踪、用户手动触发数据刷新。API 接口负责验证请求并将任务放入队列。
*   **任务队列 (Task Queue):**
    *   使用如 Celery + (RabbitMQ/Redis) 实现。负责接收来自触发器的任务，解耦任务的产生和执行，实现异步处理，提高系统的吞吐量和可靠性。管理任务状态（等待、执行中、完成、失败）。
*   **数据获取 Worker(s):**
    *   独立的 Python 进程 (Celery Worker)，从任务队列中获取任务并执行。
    *   根据任务类型，调用相应的逻辑：
        *   与 YouTube Data API v3 交互。
        *   (可选，需谨慎) 执行网页抓取逻辑。
        *   处理 OAuth 流程并与 YouTube Analytics API 交互。
    *   是系统扩展性的关键，可以通过增加 Worker 数量来提高并发处理能力。
*   **数据处理/验证 (Python Logic):**
    *   Worker 获取原始数据后，由这部分逻辑进行：
        *   **解析:** 从 API 响应或抓取内容中提取所需字段。
        *   **转换:** 格式化数据（如日期时间、数字类型）。
        *   **验证:** 检查数据完整性和基本有效性。
        *   **去重判断:** 结合数据库已有数据，判断是否需要插入或更新（参考第 3.4 节）。
*   **数据库存储 (PostgreSQL):**
    *   使用 PostgreSQL 数据库存储结构化数据。
    *   包含之前设计的表结构 (channels, videos, snapshots, comments, analytics 等)。
    *   利用数据库约束（UNIQUE, FOREIGN KEY）保证数据一致性。
    *   提供数据持久化。
*   **日志/监控系统:**
    *   记录服务运行状态、任务执行情况、API 调用、错误信息。
    *   使用 Python `logging` 模块，可配置输出到文件、控制台或 ELK、Datadog 等集中式日志系统。
    *   监控关键指标：队列长度、任务成功/失败率、API 配额使用情况、Worker 健康状态。

**3. 交互逻辑与工作流**

**3.1. 核心流程：获取频道/视频公开数据 (API 优先)**

1.  **触发:** 调度器定时触发（如“刷新频道 X 数据”）或 API 接收到请求。
2.  **入队:** 将任务（包含 `entity_type='channel'`, `entity_id='UC...'`, `data_type='public_stats'` 等信息）放入任务队列。
3.  **出队:** Worker 获取任务。
4.  **检查更新:** Worker 查询数据库 `channels` 表，获取该 `entity_id` 的 `last_refreshed_at`。如果距离上次刷新时间未超过阈值（如 6 小时），可选择跳过或降低优先级。
5.  **API 调用:** Worker 使用 YouTube Data API v3 的 `channels.list` (part=snippet,statistics,brandingSettings) 或 `videos.list` (part=snippet,statistics,contentDetails) 获取数据。
6.  **数据处理:**
    *   解析 API 响应，提取标题、描述、订阅数、观看数、视频数、发布时间、缩略图等。
    *   验证数据有效性。
7.  **去重与存储:**
    *   **基础信息:**
        *   查询数据库中是否存在该 `youtube_channel_id` / `youtube_video_id`。
        *   **存在:** 对比基础信息（标题、描述等）是否有变化，若有则 `UPDATE` `channels` / `videos` 表，并更新 `last_refreshed_at`。
        *   **不存在:** `INSERT` 新记录到 `channels` / `videos` 表。
    *   **快照数据:**
        *   查询该频道/视频最新的快照记录 (`channel_public_stats_snapshots` / `video_public_stats_snapshots`)。
        *   **对比:** 如果新获取的统计数据（观看、订阅、点赞等）与最新快照 *显著不同* (或超过一定时间间隔，如 1 小时)，则 `INSERT` 一条新的快照记录，包含当前时间戳 `snapshot_at` 和获取到的统计数据。
        *   **相同/变化不大:** 可选择不插入，避免冗余。
8.  **日志:** 记录任务成功、获取的数据量、API 耗时。
9.  **错误处理:** 见 3.5 节。

*(如果 API 无法满足或需要更高频数据，可在此流程中替换/补充网页抓取逻辑，但需特别注意风险和稳定性)*

**3.2. 核心流程：获取评论数据 (增量)**

1.  **触发:** 通常由获取视频数据的流程触发（如果视频有新评论活动），或定期检查热门/重要视频。
2.  **入队:** 任务包含 `entity_type='video'`, `entity_id='dQw4w9WgXcQ'`, `data_type='comments'`。
3.  **出队:** Worker 获取任务。
4.  **获取锚点:** Worker 查询数据库 `comments` 表，找到该 `video_id` 下 `published_at` 最晚的评论时间戳（或 YouTube Comment ID）作为增量更新的起点。如果是首次获取，则无锚点。
5.  **API 调用:** Worker 使用 YouTube Data API v3 的 `commentThreads.list` (part=snippet,replies)，按 `videoId` 过滤，可能需要分页获取。如果找到锚点，理论上可以通过某种方式（API 可能不支持直接按时间过滤）优化，只获取更新的；更常见的是获取第一页，与库中最新评论对比，判断是否有新评论。
6.  **数据处理:** 解析 API 响应，提取评论 ID、作者、文本、发布时间、点赞数、父评论 ID（用于回复）。
7.  **去重与存储:**
    *   遍历获取到的评论。
    *   对每条评论，查询数据库 `comments` 表是否存在该 `youtube_comment_id`。
    *   **不存在:** `INSERT` 新评论记录。
    *   **存在:** (可选) 检查 `like_count` 或 `updated_at` 是否变化，如果变化则 `UPDATE`。
8.  **触发分析 (可选):** 对于新插入的评论，可以触发一个后续任务，将其送入 NLP 模块进行情感/主题分析，结果存入 `comment_analysis` 表。
9.  **日志:** 记录获取的评论数量。
10. **错误处理:** 见 3.5 节。

**3.3. 核心流程：获取用户授权的私密分析数据 (OAuth)**

1.  **触发:** 调度器定时触发（如“每日获取授权用户分析数据”）。
2.  **入队:** 任务为通用类型，如 `task_type='fetch_authorized_analytics'`。
3.  **出队:** Worker 获取任务。
4.  **查询授权:** Worker 查询 `authorized_channels` 表，获取所有 `deleted_at IS NULL` 且需要更新分析数据的记录（可根据 `last_analytics_fetched_at` 等字段判断）。
5.  **循环处理:** 对每个授权记录：
    a.  **获取 Token:** 从数据库中获取加密存储的 `refresh_token`。**解密**。
    b.  **刷新 Access Token:** 调用 Google OAuth 2.0 API，使用 `refresh_token` 获取新的 `access_token` 和 `expires_in`。
    c.  **更新 Token:** 将新的 `access_token` (加密存储) 和 `token_expiry` 更新回 `authorized_channels` 表。如果刷新失败（如授权被撤销），标记该授权记录为失效或删除。
    d.  **API 调用 (Analytics):** 使用有效的 `access_token` 调用 YouTube Analytics API。
        *   根据需要获取不同报告（频道基础指标、视频维度指标、观众画像、流量来源等）。
        *   指定 `startDate`, `endDate`, `ids=channel=={YOUTUBE_CHANNEL_ID}`, `metrics`, `dimensions` 等参数。
    e.  **数据处理:** 解析 API 响应，通常是 JSON 格式的表格数据。
    f.  **存储:**
        *   将解析后的数据 `INSERT` 或 `UPDATE` 到对应的私密数据表 (`channel_private_analytics`, `video_private_analytics`, `audience_demographics`, `traffic_sources`)。
        *   使用 `UNIQUE` 约束（如 `(authorized_channel_id, data_date, granularity)`）避免插入重复日期的相同维度数据。可采用 `INSERT ... ON CONFLICT ... DO UPDATE` 语句。
    g.  **更新状态:** 更新 `authorized_channels` 表中该记录的 `last_analytics_fetched_at` 时间戳。
6.  **日志:** 记录每个授权频道的处理结果、获取的数据量、API 耗时、Token 刷新情况。
7.  **错误处理:** 特别注意处理 OAuth 错误 (invalid\_grant)、API 权限错误、配额错误。见 3.5 节。

**3.4. 任务调度与去重**

*   **调度策略:**
    *   **高频数据 (可选，抓取为主):** 如需近实时数据（类似 ViewStats），可设置较短间隔（分钟级）的抓取任务，但需控制目标范围和频率。
    *   **公开统计快照:** 每小时或每几小时更新一次活跃/重要频道/视频。低活跃度的可降低频率（如每天）。
    *   **频道/视频基础信息:** 每天或更长周期更新一次。
    *   **评论:** 可在获取视频数据时触发，或对重点视频设置较高检查频率。
    *   **私密分析数据:** 通常每天获取前一天的数据。
*   **任务级去重:** 在任务入队前，可以检查队列中是否已存在完全相同的待处理任务（相同实体 ID、相同数据类型）。
*   **数据级去重:** 在数据处理和存储阶段，通过查询数据库已有数据和利用 `UNIQUE` 约束来避免存储冗余信息（如 3.1 和 3.3 中所述）。

**3.5. 错误处理与重试**

*   **API 错误:**
    *   **配额超限 (Quota Exceeded):** 捕获特定错误码 (如 403 `quotaExceeded`)。应实施：
        *   **指数退避 (Exponential Backoff):** 等待一段时间后重试，每次重试的等待时间逐渐增加。
        *   **任务延迟/重新入队:** 将失败的任务放回队列，并设置延迟执行。
        *   **全局速率限制:** 在 Worker 端实现对 YouTube API 的调用速率限制，避免短时脉冲触发配额限制。
        *   **监控配额使用:** 通过 API 或 Google Cloud Console 监控配额，及时调整策略或申请增加配额。
    *   **资源不可用 (Not Found 404):** 视频/频道/评论被删除或设为私密。记录错误，标记对应实体状态，通常不重试。
    *   **服务器错误 (5xx):** YouTube 服务器临时问题。适合使用指数退避重试几次。
    *   **权限错误 (401/403):**
        *   **OAuth Token 失效:** 触发 Token 刷新流程。若刷新失败，标记授权失效。
        *   **API Key 无效:** 检查配置。
        *   **私有内容:** 确认是否有权访问。
*   **抓取错误 (如果使用):**
    *   **页面结构变化:** 导致解析失败。需要人工介入更新抓取脚本。记录详细错误，发送警报。
    *   **反爬虫机制 (验证码/IP 封锁):** 需要更复杂的抓取策略（代理、验证码处理服务），失败率较高。记录错误，可能需要暂时禁用或降低抓取频率。
*   **网络错误:** 超时、连接失败等。适合重试。
*   **数据库错误:** 连接失败、写入冲突等。根据错误类型决定是否重试。
*   **重试策略:**
    *   在 Celery 等任务队列中配置自动重试机制（`autoretry_for`, `retry_kwargs`, `retry_backoff`）。
    *   设置最大重试次数，避免无限重试。
    *   对于最终失败的任务，记录到死信队列或错误日志中，供人工排查。

**4. 数据模型 (PostgreSQL SQL)**

```sql
-- 复用之前问题中提供的详细 SQL DDL 语句。
-- 这里仅列出表名作为参考，请将之前的 SQL 脚本整合到这里。

-- 1. users -- 我们平台的用户
-- 2. channels -- YouTube 频道核心信息
-- 3. channel_public_stats_snapshots -- 频道公开数据快照 (时间序列)
-- 4. videos -- YouTube 视频核心信息
-- 5. video_public_stats_snapshots -- 视频公开数据快照 (时间序列)
-- 6. comments -- 视频评论信息
-- 7. comment_analysis -- 评论 NLP 分析结果 (差异化)
-- 8. authorized_channels -- 用户授权频道信息 (用于私密数据)
-- 9. channel_private_analytics -- 频道私密分析数据 (日/月)
-- 10. video_private_analytics -- 视频私密分析数据 (日/生命周期)
-- 11. audience_demographics -- 观众画像数据 (私密)
-- 12. traffic_sources -- 流量来源数据 (私密)
-- 13. content_element_analysis -- 内容元素分析结果 (差异化)
-- 14. optimization_suggestions -- 系统生成的优化建议 (差异化)

-- 确保所有表都有必要的字段: id, created_at, updated_at, deleted_at (where applicable)
-- 确保外键、唯一约束、索引都已按需创建。
-- 确保 updated_at 的自动更新触发器已创建并应用到相关表。
```
*(请将上一回答中完整的 SQL DDL 语句复制到此处)*

**5. 外部 API 交互**

**5.1. YouTube Data API v3**

*   **认证:** 使用 API Key (用于公开数据) 或 OAuth 2.0 (如果需要代表用户操作，如发布评论，但本服务主要用于读取)。
*   **关键 Endpoint:**
    *   `channels.list`: 获取频道信息 (snippet, statistics, brandingSettings)。
    *   `videos.list`: 获取视频信息 (snippet, statistics, contentDetails)。
    *   `search.list`: 搜索频道、视频、播放列表 (谨慎使用，配额消耗大)。
    *   `commentThreads.list`: 获取视频评论 (包括回复)。
    *   `comments.list`: 获取单条评论或回复。
    *   `playlistItems.list`: 获取播放列表中的视频。
*   **配额:** 每个项目有每日配额限制 (默认 10,000 units/day)。不同请求消耗不同点数。必须精打细算，优化调用 (使用 `part` 参数只获取需要的数据，利用 ETag 缓存)。
*   **库:** 使用 Google API Python Client (`google-api-python-client`)。

**5.2. YouTube Analytics API**

*   **认证:** 必须使用 OAuth 2.0。用户需要授权你的应用访问其 YouTube Analytics 数据。需要处理完整的 OAuth 流程（获取授权码、交换 token、存储 refresh token、使用 access token、刷新 access token）。
*   **关键 Endpoint:**
    *   `reports.query`: 执行查询获取分析报告。
*   **参数:** `ids`, `startDate`, `endDate`, `metrics` (观看次数、时长、订阅数、收入、展示次数、CTR 等), `dimensions` (日期、国家、视频、流量来源、设备类型、观众年龄性别等), `filters`, `sort`。
*   **配额:** 有独立的配额限制。
*   **库:** 同样使用 `google-api-python-client`，但需要结合 OAuth 库 (如 `google-auth-oauthlib`)。

**5.3. (可选) 网页抓取注意事项**

*   **风险:** 违反 YouTube 服务条款，可能导致 IP 封禁、法律风险。不稳定，页面结构随时变化。
*   **技术:** 需要使用 `requests` + `BeautifulSoup` / `lxml`，或更复杂的工具如 `Selenium` / `Playwright` (处理 JS 渲染)。需要健壮的代理 IP 池、User-Agent 轮换、处理验证码的策略。
*   **数据:** 通常只能获取页面当前展示的数据，历史精确数据难获取。数据准确性可能不如 API。
*   **建议:** 仅作为 API 无法满足时的补充手段，且必须谨慎实施，做好监控和快速修复的准备。优先使用 API。

**6. 配置管理**

*   **方式:** 使用环境变量 (`.env` 文件配合 `python-dotenv`) 或配置文件 (`config.yaml`, `config.ini`)。
*   **关键配置项:**
    *   数据库连接信息 (Host, Port, User, Password, DB Name)。
    *   YouTube Data API Key。
    *   OAuth Client ID & Client Secret (用于 Analytics API)。
    *   任务队列连接信息 (e.g., RabbitMQ/Redis URL)。
    *   调度器任务配置 (时间间隔、任务参数)。
    *   日志级别和输出路径。
    *   (如果使用抓取) 代理配置、User-Agents 列表。
    *   重试次数、退避因子。

**7. 日志与监控**

*   **日志:**
    *   使用 Python 内置 `logging` 模块。
    *   记录关键事件：任务开始/结束、API 调用 (端点、参数、耗时、配额消耗估算)、抓取活动、数据存储操作、错误和异常 (带 Traceback)。
    *   按级别分类 (DEBUG, INFO, WARNING, ERROR, CRITICAL)。
    *   输出到文件（按日期轮转）和/或标准输出（便于容器化部署）。考虑接入集中式日志系统 (ELK, Graylog, Datadog)。
*   **监控:**
    *   **队列监控:** 任务队列长度、等待时间、Worker 数量和状态 (Celery Flower 或类似工具)。
    *   **任务执行:** 成功率、失败率、平均执行时间。
    *   **API 配额:** 监控 YouTube API 配额使用情况，设置告警阈值。
    *   **系统资源:** 服务器 CPU、内存、磁盘 I/O、网络。
    *   **数据库性能:** 连接数、慢查询。
    *   使用 Prometheus + Grafana, Datadog, New Relic 等工具进行监控和可视化。

**8. 安全性考虑**

*   **API Key / OAuth Secrets:** 绝不硬编码在代码中。使用环境变量或安全的配置管理服务。限制 API Key 的权限（如仅允许来自特定 IP）。
*   **数据库凭证:** 同样需要安全管理。
*   **OAuth Tokens:** `access_token` 和 `refresh_token` 必须加密存储在数据库中。使用强加密算法。应用层面严格控制对这些 token 的访问。
*   **输入验证:** 对来自 API 接口或用户输入的频道/视频 ID 进行格式校验，防止注入等问题。
*   **依赖库安全:** 定期更新 Python 依赖库，修复已知的安全漏洞。

**9. 部署建议**

*   **容器化:** 使用 Docker 和 Docker Compose 将服务及其依赖（如 PostgreSQL, RabbitMQ/Redis）打包，简化部署和环境一致性。
*   **环境分离:** 区分开发、测试、生产环境，使用不同的配置。
*   **进程管理:** 使用 Supervisor 或 Systemd 管理 Worker 进程，确保其稳定运行和自动重启。
*   **扩展性:** 基于 Worker 的架构易于水平扩展，根据负载增加 Worker 容器实例即可。
*   **数据库备份:** 定期备份 PostgreSQL 数据库。

**10. 未来扩展**

*   支持更多 YouTube API 功能（如播放列表管理、字幕获取）。
*   集成更多数据源（如社交媒体平台）。
*   增加更复杂的分析任务（如视频内容元素与表现关联分析）。
*   提供 Webhook 功能，当获取到特定数据时通知其他系统。
*   优化数据库查询性能，引入数据库分区或 TimescaleDB 等。

---

**说明:**

本文档提供了一个 YouTube 数据获取服务的蓝图。实际开发中需要根据具体需求细节进行调整和细化。特别是关于网页抓取的部分，需要仔细评估风险和收益。后续基于此文档，可以开始编写各个组件的 Python 代码，并创建数据库表。





以下文档从架构层面、交互逻辑、模块说明到数据库 SQL DDL，完整描述了一个基于 Python 的 YouTube 数据获取服务，涵盖从调用 YouTube API 到持久化存储的全流程，为后续代码生成打下基础。

---

## 概要  
本方案采用 **微服务+ETL** 架构，将 YouTube Data API／Analytics API 作为数据源，以 **Python** 编写的采集服务定期拉取频道、视频及统计数据，通过 **ETL 管道** 进行清洗、去重、增量更新，最终存入 **PostgreSQL**。服务通过 **RESTful 接口** 暴露状态与触发控制，支持幂等及异常重试，并在数据库层利用 **Upsert** 和 **软删除** 保证数据一致性。  

---

## 一、交互逻辑架构文档  

```mermaid
sequenceDiagram
    participant Scheduler    as 定时调度(Cron/Cloud Scheduler)
    participant IngestSvc    as 数据采集服务(Python)
    participant ETL          as ETL管道(Dagster/Airflow)
    participant Postgres     as PostgreSQL数据库
    participant API          as 控制与监控API(Flask/FastAPI)
    participant Dashboard    as 前端监控面板

    %% 1. 定时触发采集
    Scheduler->>IngestSvc: 触发“拉取任务”
    %% 2. 调用 YouTube API
    IngestSvc->>YouTubeAPI: GET /channels, /videos, /analytics
    Note right of YouTubeAPI: 支持 ETag 判断资源变化citeturn0search18
    YouTubeAPI-->>IngestSvc: 返回 JSON 数据

    %% 3. 写入原始数据
    IngestSvc->>Postgres: INSERT INTO raw_fetch (...)
    %% 4. 通知 ETL
    IngestSvc-->>ETL: 推送任务消息
    %% 5. 数据清洗与变换
    ETL->>Postgres: UPSERT INTO channels, videos, stats...
    Note right of Postgres: 使用 ON CONFLICT DO UPDATE 保证幂等citeturn0search12
    %% 6. 更新监控状态
    ETL->>API: POST /tasks/{id}/status
    API-->>Dashboard: WebSocket 推送状态更新
    Dashboard-->>用户: 显示最新采集与入库进度
```

**说明：**  
1. **定时调度**：可使用 Cron、Kubernetes CronJob 或云平台 Scheduler 触发 Python 程序citeturn0search13。  
2. **数据采集服务**：基于 Google 官方客户端（`google-api-python-client`），支持 OAuth2 认证与 API 限额管理citeturn0search2。  
3. **ETL 管道**：可选 Dagster、Airflow 等框架编排，分离“原始入库”与“清洗入库”两阶段，按 **日期分区** 存储时序数据citeturn0search1。  
4. **数据库层**：PostgreSQL 做为 OLTP 存储，利用唯一约束与 Upsert 实现增量更新，`deleted_at` 软删除保留历史citeturn0search12。  
5. **监控与控制 API**：使用 Flask 或 FastAPI 提供任务状态查询、手动重试等操作citeturn0search22。  

---

## 二、模块说明书  

| 模块         | 技术选型                                       | 功能描述                                                     |
| ------------ | ---------------------------------------------- | ------------------------------------------------------------ |
| **调度层**   | Cron/K8s CronJob                               | 定期调用控制 API，生成拉取任务记录。                         |
| **采集服务** | Python + Requests / `google-api-python-client` | 1. 获取 OAuth2 凭证<br/>2. 调用 YouTube Data & Analytics API<br/>3. 写入 `raw_fetch` 表 |
| **ETL 管道** | Dagster / Airflow                              | 1. 读取原始表 `raw_fetch`<br/>2. 清洗、解构 JSON<br/>3. Upsert 到业务表 |
| **数据库层** | PostgreSQL                                     | 存储维度表（`channels`,`videos`）和事实表（`video_stats`,`revenue_stats`） |
| **API 服务** | FastAPI                                        | 任务管理：创建、查询、重试；服务健康检查                     |
| **监控面板** | React/Vue + WebSocket                          | 实时展示采集进度、错误日志，以及最近数据更新时间             |

### 接口设计

```yaml
POST /tasks
  - 请求体: { "type": "full"|"incremental", "params": {...} }
  - 功能: 创建采集任务

GET /tasks/{task_id}
  - 返回: { "status": "pending"|"running"|"success"|"failed", "message": "...", "started_at": "...", "ended_at": "..." }

POST /tasks/{task_id}/retry
  - 功能: 重试指定失败任务

GET /health
  - 功能: 服务健康检查
```

---

## 三、数据库 SQL DDL

```sql
-- 原始抓取表，存储原始 JSON 响应
CREATE TABLE raw_fetch (
  id          BIGSERIAL      PRIMARY KEY,
  task_id     UUID           NOT NULL,                  -- 调度任务标识
  endpoint    TEXT           NOT NULL,                  -- API 端点（channels/videos/analytics）
  response    JSONB          NOT NULL,                  -- 原始 JSON 数据
  fetched_at  TIMESTAMPTZ   NOT NULL DEFAULT NOW(),    -- 抓取时间
  etag        TEXT,                                    -- YouTube 返回的 ETag
  created_at  TIMESTAMPTZ   NOT NULL DEFAULT NOW()
);

-- 频道维度表
CREATE TABLE channels (
  id            BIGSERIAL      PRIMARY KEY,
  channel_id    VARCHAR(64)    NOT NULL UNIQUE,
  title         VARCHAR(255)   NOT NULL,
  description   TEXT,
  published_at  TIMESTAMPTZ    NOT NULL,
  thumbnails    JSONB,
  created_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  deleted_at    TIMESTAMPTZ
);

-- 视频维度表
CREATE TABLE videos (
  id            BIGSERIAL      PRIMARY KEY,
  video_id      VARCHAR(64)    NOT NULL UNIQUE,
  channel_id    BIGINT         NOT NULL REFERENCES channels(id),
  title         VARCHAR(255)   NOT NULL,
  description   TEXT,
  published_at  TIMESTAMPTZ    NOT NULL,
  thumbnails    JSONB,
  created_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  deleted_at    TIMESTAMPTZ
);

-- 视频统计事实表
CREATE TABLE video_stats (
  id            BIGSERIAL      PRIMARY KEY,
  video_id      BIGINT         NOT NULL REFERENCES videos(id),
  stat_date     DATE           NOT NULL,
  views         BIGINT         NOT NULL DEFAULT 0,
  watch_time    BIGINT         NOT NULL DEFAULT 0,       -- 单位：秒
  created_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  deleted_at    TIMESTAMPTZ
);

-- 收益统计表
CREATE TABLE revenue_stats (
  id                 BIGSERIAL      PRIMARY KEY,
  video_id           BIGINT         NOT NULL REFERENCES videos(id),
  stat_date          DATE           NOT NULL,
  estimated_revenue  NUMERIC(12,2)  NOT NULL DEFAULT 0.00,
  cpm                NUMERIC(8,2)   NOT NULL DEFAULT 0.00,
  rpm                NUMERIC(8,2)   NOT NULL DEFAULT 0.00,
  created_at         TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at         TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  deleted_at         TIMESTAMPTZ
);
```

