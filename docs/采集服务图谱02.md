```mermaid
flowchart TB
    %% 定义节点
    Trigger["任务触发器(Scheduler/API接口)"]
    Queue["任务队列(Celery+RabbitMQ/Redis)"]
    Worker["数据获取Worker(Python进程)"]
    Processing["数据处理逻辑(解析/验证/去重)"]
    DB[(PostgreSQL数据库)]
    YT_API{"YouTube API"}
    YT_Web{"YouTube网页(可选抓取)"}
    Monitor["日志/监控系统"]
    User["用户界面/外部系统"]
    
    %% 定义连接
    Trigger -->|"周期性触发或接收外部请求"| Queue
    User -->|"添加频道/手动刷新请求"| Trigger
    Queue -->|"任务出队"| Worker
    Worker -->|"调用API"| YT_API
    Worker -.->|"可选抓取(谨慎使用)"| YT_Web
    YT_API -->|"JSON响应"| Worker
    YT_Web -.->|"HTML响应"| Worker
    Worker -->|"原始数据"| Processing
    Processing -->|"查询最新数据"| DB
    DB -->|"返回现有记录"| Processing
    Processing -->|"INSERT/UPDATE增量更新"| DB
    Worker -->|"记录API调用/错误日志"| Monitor
    Processing -->|"记录处理结果"| Monitor
    Monitor -->|"状态更新/警报"| User
    
    %% 子图分组
    subgraph "核心数据采集流程"
        Queue
        Worker
        Processing
    end
    
    subgraph "外部数据源"
        YT_API
        YT_Web
    end
    
    subgraph "持久化存储"
        DB
    end
    
    %% 样式设置
    style Trigger fill:#f9f,stroke:#333,stroke-width:2px
    style Queue fill:#bbf,stroke:#333,stroke-width:1px
    style Worker fill:#bfb,stroke:#333,stroke-width:1px
    style Processing fill:#bfb,stroke:#333,stroke-width:1px
    style DB fill:#fdb,stroke:#333,stroke-width:1px
    style YT_API fill:#ddf,stroke:#333,stroke-width:1px
    style YT_Web fill:#ddf,stroke:#333,stroke-width:1px
    style Monitor fill:#fbb,stroke:#333,stroke-width:1px
```