```mermaid
sequenceDiagram
    participant Scheduler as 任务触发器(Scheduler/API)
    participant Queue as 任务队列(Celery+RabbitMQ)
    participant Worker as 数据获取Worker
    participant Processor as 数据处理/验证
    participant DB as PostgreSQL数据库
    participant YT as YouTube APIs
    participant Monitor as 日志/监控系统
    participant User as 用户界面/外部请求

    %% 定时任务流程
    Scheduler->>Queue: 触发周期性任务(定时刷新频道数据)
    Note over Queue: 任务入队并去重(相同entity_id和data_type)
    
    %% 手动触发流程
    User->>Scheduler: 发起数据获取请求(如添加新频道)
    Scheduler->>Queue: 创建并入队任务
    
    %% 任务执行流程
    Queue->>Worker: Worker从队列获取任务
    Worker->>DB: 检查更新必要性(查询last_refreshed_at)
    DB-->>Worker: 返回最近更新时间
    
    %% API调用和数据获取
    Worker->>YT: 调用YouTube Data API
    Note right of YT: channels.list, videos.list, commentThreads.list等
    YT-->>Worker: 返回JSON格式数据
    Worker->>Monitor: 记录API调用情况和配额使用
    
    %% OAuth流程(针对私密数据)
    alt 需要获取授权数据
        Worker->>DB: 获取加密的refresh_token
        DB-->>Worker: 返回用户授权信息
        Worker->>YT: 使用refresh_token获取新access_token
        YT-->>Worker: 返回有效access_token
        Worker->>DB: 更新access_token和有效期
        Worker->>YT: 调用YouTube Analytics API
        YT-->>Worker: 返回私密分析数据
    end
    
    %% 数据处理流程
    Worker->>Processor: 传递原始数据进行处理
    Processor->>DB: 查询已有数据(用于增量更新)
    DB-->>Processor: 返回最新记录
    
    %% 数据存储逻辑
    alt 新实体(频道/视频)
        Processor->>DB: INSERT新记录
    else 更新现有实体
        Processor->>DB: UPDATE基础信息
    end
    
    alt 统计数据显著变化或时间间隔足够
        Processor->>DB: INSERT新快照记录
    end
    
    %% 错误处理流程
    alt API错误(配额超限)
        YT-->>Worker: 返回配额错误(403)
        Worker->>Queue: 延迟重试(指数退避)
        Worker->>Monitor: 记录错误并发送警报
    else 网络错误
        Worker->>Queue: 安排立即重试
    else 解析错误
        Processor->>Monitor: 记录详细错误信息
    end
    
    %% 完成通知
    Processor->>Monitor: 记录任务完成状态
    Monitor-->>User: (可选)通知数据更新完成
```