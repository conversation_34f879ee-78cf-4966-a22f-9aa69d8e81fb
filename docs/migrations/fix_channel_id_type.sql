-- 迁移脚本：将ytb_channel_stats_snapshots表中的channel_id从bigint改为varchar(50)
-- 创建日期: 2024-05-31

-- 1. 先创建临时表存储现有数据
CREATE TEMP TABLE temp_channel_snapshots AS
SELECT s.id, c.channel_id, s.view_count, s.subscriber_count, s.video_count, 
       s.snapshot_at, s.created_at, s.updated_at, s.deleted_at
FROM ytb_channel_stats_snapshots s
JOIN ytb_channels c ON s.channel_id = c.id;

-- 2. 修改表结构 - 删除外键约束，修改字段类型
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_type = 'FOREIGN KEY' 
    AND table_name = 'ytb_channel_stats_snapshots'
    AND constraint_name LIKE '%channel_id%'
  ) THEN
    EXECUTE (
      SELECT 'ALTER TABLE ytb_channel_stats_snapshots DROP CONSTRAINT ' || constraint_name
      FROM information_schema.table_constraints
      WHERE constraint_type = 'FOREIGN KEY' 
      AND table_name = 'ytb_channel_stats_snapshots'
      AND constraint_name LIKE '%channel_id%'
      LIMIT 1
    );
  END IF;
END $$;

-- 修改字段类型
ALTER TABLE ytb_channel_stats_snapshots 
ALTER COLUMN channel_id TYPE VARCHAR(50) USING NULL;

-- 添加外键约束
ALTER TABLE ytb_channel_stats_snapshots
ADD CONSTRAINT fk_channel_stats_channel_id 
FOREIGN KEY (channel_id) REFERENCES ytb_channels(channel_id);

-- 3. 从临时表恢复数据
-- 清空现有数据
TRUNCATE ytb_channel_stats_snapshots;

-- 从临时表恢复数据
INSERT INTO ytb_channel_stats_snapshots (
    id, channel_id, view_count, subscriber_count, video_count,
    snapshot_at, created_at, updated_at, deleted_at
)
SELECT id, channel_id, view_count, subscriber_count, video_count,
       snapshot_at, created_at, updated_at, deleted_at
FROM temp_channel_snapshots;

-- 重置序列
SELECT setval(pg_get_serial_sequence('ytb_channel_stats_snapshots', 'id'), 
       (SELECT MAX(id) FROM ytb_channel_stats_snapshots) + 1);

-- 4. 重建索引
-- 删除旧索引（如果有）
DROP INDEX IF EXISTS idx_ytb_channel_stats_channel_id_snapshot_at;

-- 创建新索引
CREATE INDEX idx_ytb_channel_stats_channel_id_snapshot_at 
ON ytb_channel_stats_snapshots(channel_id, snapshot_at);

-- 日志记录
INSERT INTO ytb_tasks (
    id, task_type, status, parameters, result, 
    started_at, completed_at
)
VALUES (
    gen_random_uuid(), 'database_migration', 'success',
    '{"migration": "ytb_channel_stats_snapshots.channel_id to VARCHAR(50)"}',
    '{"description": "Successfully migrated channel_id column from BIGINT to VARCHAR(50)"}',
    NOW(), NOW()
); 