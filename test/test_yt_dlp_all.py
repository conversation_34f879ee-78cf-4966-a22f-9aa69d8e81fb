import subprocess
import sys
import json
import os

# 修复导入路径问题
# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
root_dir = os.path.dirname(current_dir)
# 将项目根目录添加到Python的模块搜索路径
sys.path.insert(0, root_dir)

# import psycopg2 # Removed direct psycopg2 import
# from psycopg2 import sql # Removed direct psycopg2 import
from dotenv import load_dotenv
from datetime import datetime, timedelta
from src.dao.youtube_dao import YouTubeDao # Use the DAO
from src.dao.channel_dao import ChannelDao

# 加载 .env 文件中的环境变量
load_dotenv()

def parse_duration(duration_str):
    """将 yt-dlp 的 duration (秒) 转换为 INTERVAL 类型可接受的字符串。"""
    if duration_str is None:
        return None
    try:
        seconds = int(duration_str)
        return timedelta(seconds=seconds)
    except (ValueError, TypeError):
        return None

def parse_upload_date(date_str):
    """将 yt-dlp 的 upload_date (YYYYMMDD) 转换为 datetime 对象。"""
    if date_str is None:
        return None
    try:
        return datetime.strptime(date_str, '%Y%m%d')
    except (ValueError, TypeError):
        return None

# Removed get_db_connection function
# Removed ensure_channel_exists function (will use DAO method)

def process_and_save_video_via_dao(youtube_dao: YouTubeDao, channel_dao: ChannelDao, video_json_str: str):
    """解析单条视频 JSON 数据并使用 DAO 保存到数据库。"""
    try:
        data = json.loads(video_json_str)
    except json.JSONDecodeError as e:
        print(f"错误: 解析 JSON 失败: {e}\n原始数据: {video_json_str[:200]}...", file=sys.stderr)
        return False # 表示处理失败

    youtube_id = data.get('id')
    if not youtube_id:
        print(f"警告: 视频缺少 ID，跳过。数据: {video_json_str[:200]}...", file=sys.stderr)
        return False

    channel_id = data.get('playlist_id')
    # channel_title = data.get('channel')

    # 如果频道ID确实无法获取，则无法插入视频
    if not channel_id:
         print(f"警告: 视频 {youtube_id} 缺少 channel_id，无法保存。", file=sys.stderr)
         return False

    video_data = YouTubeDao.get_video_by_youtube_id(youtube_id)
    if video_data:
        print(f"视频 {youtube_id} 已存在，跳过。", file=sys.stderr)
        return False


    video_data = {
        'id': youtube_id,
        'channelId': channel_id,
        'title': data.get('title'),
        'description': data.get('description'),
        'publishedAt': parse_upload_date(data.get('upload_date')),
        'duration': parse_duration(data.get('duration')),
        'tags': data.get('tags'),
        'categoryId': data.get('categories')[0] if data.get('categories') else None, # yt-dlp 可能没有这个字段
        'thumbnailUrl': data.get('thumbnail'),
        'thumbnails': data.get('thumbnails'),
        'viewCount': data.get('view_count'),
        'likeCount': data.get('like_count') if data.get('like_count') is not None else 0,
        'commentCount': data.get('comment_count') if data.get('comment_count') is not None else 0,
        'lastRefreshedAt': datetime.now(),
        # 其他字段根据需要添加或通过 API 补充
    }

    try:
        # 1. 确保频道存在 (调用 DAO 方法)
        # Assuming DAO handles potential None channel_title
        result=channel_dao.get_channel_by_youtube_id(channel_id)
        if result is None:
            print(f"警告: 频道 {channel_id} 不存在。", file=sys.stderr)
            return False

        # youtube_dao.ensure_channel_exists(channel_id, channel_title or 'Unknown Title')

        # 2. 保存视频数据 (调用 DAO 方法, assuming it handles UPSERT)
        youtube_dao.save_video(video_data)
        # print(f"成功处理视频: {youtube_id}") # Optional: for verbose logging
        return True # 表示处理成功

    except Exception as e:
        # Catching a broad exception here, specific exceptions depend on DAO implementation
        print(f"数据库操作错误 (处理视频 {youtube_id} via DAO): {e}", file=sys.stderr)
        # Log the data that caused the error for debugging
        # print(f"Failed data: {video_data}", file=sys.stderr)
        return False # 表示处理失败

def get_channel_videos_json_and_save(channel_url):
    """
    使用 yt-dlp 获取频道所有视频的 JSON 数据，解析并使用 YouTubeDao 保存到数据库。
    """
    # 确保 yt-dlp 命令存在
    try:
        # 尝试获取 yt-dlp 版本信息，如果命令不存在会抛出 FileNotFoundError
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True, text=True)
    except FileNotFoundError:
        print("错误: 'yt-dlp' 命令未找到或无法执行。", file=sys.stderr)
        print("请确保 yt-dlp 已经安装并且在您的系统 PATH 环境变量中。", file=sys.stderr)
        sys.exit(1) # 退出脚本
    except subprocess.CalledProcessError as e:
        print(f"错误: 执行 'yt-dlp --version' 失败: {e}", file=sys.stderr)
        sys.exit(1)

    # 构建 yt-dlp 命令
    # 使用列表传递参数更安全，避免 shell 注入风险
    command = [
        'yt-dlp',
        '--skip-download',  # 不下载视频
        '-j',               # 输出 JSON 格式 (等同于 --dump-json)
        '--flat-playlist',  # 通常更快地获取列表，但不包含完整元数据，如果需要完整数据则去掉此项
        # '--print', '%(view_count)s %(categories)j %(thumbnail)s',  # 直接打印关键字段
        # '--no-flat-playlist',   # 强制获取每个视频的完整元数据
        '--ignore-errors', # 可以选择忽略下载或提取错误，继续处理下一个视频
        channel_url         # 目标频道 URL
    ]

    command_str = ' '.join(command) # 仅用于打印
    print(f"即将执行命令: {command_str}")
    print("-" * 30)

    # Removed conn = None
    saved_count = 0
    failed_count = 0
    processed_count = 0
    # Removed commit_batch_size and related logic (assuming DAO handles it)

    youtube_dao = None # Initialize DAO variable
    try:
        # 实例化 DAO (可能在构造函数中连接数据库)
        youtube_dao = YouTubeDao()
        channel_dao = ChannelDao()
        print("YouTubeDao 实例化成功。")

        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True, # 使用文本模式处理输出
            bufsize=1, # 行缓冲
            universal_newlines=True # 兼容不同系统的换行符
        )

        print("----- 开始处理 yt-dlp 输出并存入数据库 (via DAO) ----- ")
        if process.stdout:
            for line in process.stdout:
                processed_count += 1
                if line.strip(): # 确保不是空行
                    # 使用 DAO 处理和保存
                    if process_and_save_video_via_dao(youtube_dao,channel_dao, line):
                        saved_count += 1
                    else:
                        failed_count += 1

                    # Removed batch commit logic

                # 打印进度
                if processed_count % 10 == 0:
                     print(f"已处理: {processed_count}, 成功保存: {saved_count}, 失败: {failed_count}", end='\r')

        print("\n----- yt-dlp 输出处理完毕 ----- ")

        # Removed final commit logic

        # 检查 yt-dlp 进程的 stderr
        stderr_output = ""
        if process.stderr:
            stderr_output = process.stderr.read()
            if stderr_output:
                print("\n----- yt-dlp 错误/日志 (stderr) ----- ")
                print(stderr_output, end='')
                print("----- /yt-dlp 错误/日志 (stderr) ----- ")

        # 等待命令执行完成并检查返回码
        process.wait()
        print("-" * 30)
        if process.returncode == 0:
            print("yt-dlp 命令执行成功。")
        else:
            print(f"警告: yt-dlp 命令执行可能存在问题，返回码: {process.returncode}")

        print(f"\n处理总结: 总计处理 {processed_count} 条记录, 成功保存 {saved_count} 条, 失败 {failed_count} 条。")

    except Exception as e:
        # Catch exceptions during DAO instantiation or processing
        print(f"\n执行过程中发生意外错误: {e}", file=sys.stderr)
        # Log known stats before exiting
        print(f"\n处理总结 (可能不完整): 总计处理 {processed_count} 条记录, 成功保存 {saved_count} 条, 失败 {failed_count} 条。")
        sys.exit(1)
    finally:
        # Removed connection closing (assuming DAO handles its lifecycle)
        # If DAO needs explicit cleanup, add youtube_dao.close() or similar here
        print("脚本执行完毕。")

# --- 使用示例 ---
if __name__ == "__main__":
    """
    1. 获取频道下所有的视频
    """

    # 获取所有频道
    channels = ChannelDao.get_all_channels()
    for channel in channels:
        print(f"频道 ID: {channel[1]}, 频道标题: {channel[2]}")
        channel_id_to_check = channel[1]
        print(f"开始获取频道 {channel_id_to_check} 的视频数据...")
        target_channel_url = f"https://www.youtube.com/channel/{channel_id_to_check}"
        get_channel_videos_json_and_save(target_channel_url)



    # 请将下面的 URL 替换为你要查询的频道 URL
    # 示例：YouTube Creators 官方频道
    # target_channel_url = "https://www.youtube.com/@lingdujieshuo"
    # target_channel_url = "https://www.youtube.com/@OpenAI"
    
    # target_channel_url = "https://www.youtube.com/channel/UCXZCJLdBC09xxGZ6gcdrc6A"

    # 你也可以从命令行参数获取 URL
    # import argparse
    # parser = argparse.ArgumentParser(description='获取 YouTube 频道视频 JSON 数据')
    # parser.add_argument('channel_url', help='目标 YouTube 频道的 URL')
    # args = parser.parse_args()
    # target_channel_url = args.channel_url

    # get_channel_videos_json_and_save(target_channel_url)
