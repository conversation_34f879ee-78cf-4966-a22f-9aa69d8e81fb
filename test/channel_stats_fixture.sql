-- 初始化测试数据生成脚本
-- 基于频道ID: TESTUCQOt3FI6FBeYecatZq7auyA

-- 清理现有测试数据
DELETE FROM ytb_channel_stats_snapshots WHERE channel_id = 'TESTUCQOt3FI6FBeYecatZq7auyA';
DELETE FROM ytb_video_stats_snapshots WHERE video_youtube_id IN (SELECT youtube_id FROM ytb_videos WHERE channel_id = 'TESTUCQOt3FI6FBeYecatZq7auyA');
DELETE FROM ytb_videos WHERE channel_id = 'TESTUCQOt3FI6FBeYecatZq7auyA';
DELETE FROM ytb_channel_time_statistics WHERE channel_id = 'TESTUCQOt3FI6FBeYecatZq7auyA';
DELETE FROM ytb_tasks WHERE parameters::jsonb->>'channel_id' = 'TESTUCQOt3FI6FBeYecatZq7auyA';
DELETE FROM ytb_raw_fetch WHERE response::jsonb->>'id' = 'TESTUCQOt3FI6FBeYecatZq7auyA';
DELETE FROM ytb_channels WHERE channel_id = 'TESTUCQOt3FI6FBeYecatZq7auyA';

-- 创建测试频道
INSERT INTO ytb_channels (
    channel_id, title, description, custom_url, published_at, country, 
    view_count, subscriber_count, video_count, topic_categories, thumbnail_url, 
    banner_url, last_refreshed_at, is_verified
) VALUES (
    'TESTUCQOt3FI6FBeYecatZq7auyA',
    '测试频道',
    '这是一个用于测试的YouTube频道',
    '@TestChannel',
    '2020-01-01 00:00:00+00',
    'CN',
    1000000,
    50000,
    100,
    ARRAY['Music', 'Technology'],
    'https://example.com/thumbnail.jpg',
    'https://example.com/banner.jpg',
    NOW(),
    TRUE
);

-- 创建测试任务记录
INSERT INTO ytb_tasks (
    task_type, status, parameters, result, started_at, completed_at
) VALUES (
    'channel_info',
    'success',
    '{"channel_id": "TESTUCQOt3FI6FBeYecatZq7auyA"}',
    '{"success": true, "message": "频道信息获取成功"}',
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '59 minutes'
);

-- 创建测试原始数据抓取记录
INSERT INTO ytb_raw_fetch (
    task_id, endpoint, response, params, etag, quota_cost, fetched_at
) VALUES (
    (SELECT id FROM ytb_tasks WHERE parameters::jsonb->>'channel_id' = 'TESTUCQOt3FI6FBeYecatZq7auyA' LIMIT 1),
    'channels',
    '{"kind": "youtube#channelListResponse", "etag": "test_etag", "id": "TESTUCQOt3FI6FBeYecatZq7auyA", "snippet": {"title": "测试频道"}}',
    '{"part": "snippet,statistics", "id": "TESTUCQOt3FI6FBeYecatZq7auyA"}',
    'test_etag_value',
    1,
    NOW() - INTERVAL '1 hour'
);

-- 创建示例视频和快照
DO $$
DECLARE
    video_count INTEGER := 10;
    base_date TIMESTAMP := NOW() - INTERVAL '30 days';
    video_youtube_id VARCHAR(50);
BEGIN
    FOR i IN 1..video_count LOOP
        video_youtube_id := 'TEST_vid_' || LPAD(i::text, 8, '0');
        
        -- 插入视频
        INSERT INTO ytb_videos (
            youtube_id, channel_id, title, description, published_at, 
            duration, dimension, caption, licensed_content, tags, 
            category_id, thumbnail_url, view_count, 
            like_count, comment_count, privacy_status, last_refreshed_at
        ) VALUES (
            video_youtube_id,
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            '测试视频 ' || i,
            '这是测试视频描述 ' || i,
            base_date + (i || ' days')::INTERVAL,
            (FLOOR(RANDOM() * 600) + 60) * INTERVAL '1 second',
            '2d',
            i % 2 = 0,
            TRUE,
            ARRAY['测试', '示例', '标签' || i],
            10,
            'https://example.com/thumbnail' || i || '.jpg',
            FLOOR(RANDOM() * 50000 + 1000),
            FLOOR(RANDOM() * 5000 + 100),
            FLOOR(RANDOM() * 500 + 10),
            'public',
            NOW()
        );
        
        -- 为该视频创建历史快照
        FOR j IN 1..5 LOOP
            INSERT INTO ytb_video_stats_snapshots (
                video_youtube_id, view_count, like_count, dislike_count, 
                favorite_count, comment_count, snapshot_at
            ) VALUES (
                video_youtube_id,
                FLOOR(RANDOM() * 50000 + j*1000),
                FLOOR(RANDOM() * 5000 + j*100),
                FLOOR(RANDOM() * 50 + j*10),
                FLOOR(RANDOM() * 100 + j*5),
                FLOOR(RANDOM() * 500 + j*10),
                base_date + (i || ' days')::INTERVAL + (j || ' hours')::INTERVAL
            );
        END LOOP;
    END LOOP;
END $$;

-- 创建频道快照数据
DO $$
DECLARE
    curr_date TIMESTAMP := NOW() - INTERVAL '30 days';
    end_date TIMESTAMP := NOW();
    snapshot_count INTEGER := 0;
BEGIN
    -- 每天创建3条快照
    WHILE curr_date <= end_date LOOP
        -- 早上快照
        INSERT INTO ytb_channel_stats_snapshots (
            channel_id, view_count, subscriber_count, video_count, snapshot_at
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            1000000 + FLOOR(RANDOM() * 10000 + snapshot_count * 500),
            50000 + FLOOR(RANDOM() * 100 + snapshot_count * 10),
            100 + FLOOR(snapshot_count / 10),
            curr_date + INTERVAL '9 hours' + (RANDOM() * INTERVAL '2 hours')
        );
        
        -- 中午快照
        INSERT INTO ytb_channel_stats_snapshots (
            channel_id, view_count, subscriber_count, video_count, snapshot_at
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            1000000 + FLOOR(RANDOM() * 10000 + snapshot_count * 500 + 1000),
            50000 + FLOOR(RANDOM() * 100 + snapshot_count * 10 + 20),
            100 + FLOOR(snapshot_count / 10),
            curr_date + INTERVAL '14 hours' + (RANDOM() * INTERVAL '2 hours')
        );
        
        -- 晚上快照
        INSERT INTO ytb_channel_stats_snapshots (
            channel_id, view_count, subscriber_count, video_count, snapshot_at
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            1000000 + FLOOR(RANDOM() * 10000 + snapshot_count * 500 + 2000),
            50000 + FLOOR(RANDOM() * 100 + snapshot_count * 10 + 40),
            100 + FLOOR(snapshot_count / 10),
            curr_date + INTERVAL '20 hours' + (RANDOM() * INTERVAL '2 hours')
        );
        
        curr_date := curr_date + INTERVAL '1 day';
        snapshot_count := snapshot_count + 1;
    END LOOP;
END $$;

-- 创建频道时间段统计数据
DO $$
DECLARE
    curr_date DATE := CURRENT_DATE - INTERVAL '30 days';
    end_date DATE := CURRENT_DATE;
BEGIN
    -- 日统计
    WHILE curr_date <= end_date LOOP
        INSERT INTO ytb_channel_time_statistics (
            channel_id, period_type, period_start, period_end,
            start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
            start_views, end_views, view_change, view_growth_rate,
            start_revenue, end_revenue, revenue_change, revenue_growth_rate,
            year, month, day
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            'day',
            curr_date,
            curr_date,
            FLOOR(RANDOM() * 1000 + 50000),
            FLOOR(RANDOM() * 1000 + 51000),
            FLOOR(RANDOM() * 200 + 50),
            (RANDOM() * 1 + 0.1)::NUMERIC(6,2),
            FLOOR(RANDOM() * 10000 + 1000000),
            FLOOR(RANDOM() * 10000 + 1010000),
            FLOOR(RANDOM() * 5000 + 1000),
            (RANDOM() * 1 + 0.1)::NUMERIC(6,2),
            (RANDOM() * 100 + 50)::NUMERIC(12,2),
            (RANDOM() * 100 + 60)::NUMERIC(12,2),
            (RANDOM() * 20 + 5)::NUMERIC(12,2),
            (RANDOM() * 10 + 5)::NUMERIC(6,2),
            EXTRACT(YEAR FROM curr_date),
            EXTRACT(MONTH FROM curr_date),
            EXTRACT(DAY FROM curr_date)
        );
        
        curr_date := curr_date + INTERVAL '1 day';
    END LOOP;
    
    -- 周统计 (最近4周)
    FOR i IN 0..3 LOOP
        INSERT INTO ytb_channel_time_statistics (
            channel_id, period_type, period_start, period_end,
            start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
            start_views, end_views, view_change, view_growth_rate,
            start_revenue, end_revenue, revenue_change, revenue_growth_rate,
            year, week
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            'week',
            CURRENT_DATE - INTERVAL '28 days' + (i * 7 || ' days')::INTERVAL,
            CURRENT_DATE - INTERVAL '22 days' + (i * 7 || ' days')::INTERVAL,
            FLOOR(RANDOM() * 5000 + 50000),
            FLOOR(RANDOM() * 5000 + 51000),
            FLOOR(RANDOM() * 1000 + 100),
            (RANDOM() * 2 + 0.5)::NUMERIC(6,2),
            FLOOR(RANDOM() * 50000 + 1000000),
            FLOOR(RANDOM() * 50000 + 1050000),
            FLOOR(RANDOM() * 20000 + 5000),
            (RANDOM() * 2 + 0.5)::NUMERIC(6,2),
            (RANDOM() * 500 + 100)::NUMERIC(12,2),
            (RANDOM() * 500 + 150)::NUMERIC(12,2),
            (RANDOM() * 100 + 20)::NUMERIC(12,2),
            (RANDOM() * 20 + 5)::NUMERIC(6,2),
            EXTRACT(YEAR FROM CURRENT_DATE),
            EXTRACT(WEEK FROM CURRENT_DATE - INTERVAL '28 days' + (i * 7 || ' days')::INTERVAL)
        );
    END LOOP;
    
    -- 月统计 (最近3个月)
    FOR i IN 0..2 LOOP
        INSERT INTO ytb_channel_time_statistics (
            channel_id, period_type, period_start, period_end,
            start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
            start_views, end_views, view_change, view_growth_rate,
            start_revenue, end_revenue, revenue_change, revenue_growth_rate,
            year, month
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            'month',
            DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' + (i || ' months')::INTERVAL,
            (DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' + ((i+1) || ' months')::INTERVAL - INTERVAL '1 day'),
            FLOOR(RANDOM() * 10000 + 45000),
            FLOOR(RANDOM() * 10000 + 55000),
            FLOOR(RANDOM() * 5000 + 1000),
            (RANDOM() * 10 + 2)::NUMERIC(6,2),
            FLOOR(RANDOM() * 100000 + 950000),
            FLOOR(RANDOM() * 100000 + 1050000),
            FLOOR(RANDOM() * 80000 + 20000),
            (RANDOM() * 8 + 2)::NUMERIC(6,2),
            (RANDOM() * 2000 + 500)::NUMERIC(12,2),
            (RANDOM() * 2000 + 700)::NUMERIC(12,2),
            (RANDOM() * 500 + 100)::NUMERIC(12,2),
            (RANDOM() * 30 + 10)::NUMERIC(6,2),
            EXTRACT(YEAR FROM DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' + (i || ' months')::INTERVAL),
            EXTRACT(MONTH FROM DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '3 months' + (i || ' months')::INTERVAL)
        );
    END LOOP;
    
    -- 季度统计 (最近2个季度)
    FOR i IN 0..1 LOOP
        INSERT INTO ytb_channel_time_statistics (
            channel_id, period_type, period_start, period_end,
            start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
            start_views, end_views, view_change, view_growth_rate,
            start_revenue, end_revenue, revenue_change, revenue_growth_rate,
            year, quarter
        ) VALUES (
            'TESTUCQOt3FI6FBeYecatZq7auyA',
            'quarter',
            DATE_TRUNC('quarter', CURRENT_DATE) - INTERVAL '6 months' + (i*3 || ' months')::INTERVAL,
            (DATE_TRUNC('quarter', CURRENT_DATE) - INTERVAL '6 months' + ((i+1)*3 || ' months')::INTERVAL - INTERVAL '1 day'),
            FLOOR(RANDOM() * 20000 + 40000),
            FLOOR(RANDOM() * 20000 + 60000),
            FLOOR(RANDOM() * 10000 + 2000),
            (RANDOM() * 25 + 5)::NUMERIC(6,2),
            FLOOR(RANDOM() * 300000 + 900000),
            FLOOR(RANDOM() * 300000 + 1200000),
            FLOOR(RANDOM() * 200000 + 50000),
            (RANDOM() * 25 + 5)::NUMERIC(6,2),
            (RANDOM() * 6000 + 1000)::NUMERIC(12,2),
            (RANDOM() * 6000 + 1500)::NUMERIC(12,2),
            (RANDOM() * 1000 + 300)::NUMERIC(12,2),
            (RANDOM() * 50 + 15)::NUMERIC(6,2),
            EXTRACT(YEAR FROM DATE_TRUNC('quarter', CURRENT_DATE) - INTERVAL '6 months' + (i*3 || ' months')::INTERVAL),
            EXTRACT(QUARTER FROM DATE_TRUNC('quarter', CURRENT_DATE) - INTERVAL '6 months' + (i*3 || ' months')::INTERVAL)
        );
    END LOOP;
    
    -- 年度统计 (今年)
    INSERT INTO ytb_channel_time_statistics (
        channel_id, period_type, period_start, period_end,
        start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
        start_views, end_views, view_change, view_growth_rate,
        start_revenue, end_revenue, revenue_change, revenue_growth_rate,
        year
    ) VALUES (
        'TESTUCQOt3FI6FBeYecatZq7auyA',
        'year',
        DATE_TRUNC('year', CURRENT_DATE),
        CURRENT_DATE,
        35000,
        55000,
        20000,
        57.14,
        800000,
        1200000,
        400000,
        50.00,
        10000.00,
        15000.00,
        5000.00,
        50.00,
        EXTRACT(YEAR FROM CURRENT_DATE)
    );
END $$;

-- 输出成功消息
SELECT 'SUCCESS: 生成了完整的测试数据，包括频道、视频、统计快照和时间统计数据。' AS message;
