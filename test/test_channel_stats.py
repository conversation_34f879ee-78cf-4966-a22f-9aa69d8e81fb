#!/usr/bin/env python3
"""
测试频道统计更新工具
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.utils.channel_stats_updater import ChannelStatsUpdater
from src.utils.logger import get_logger
from src.database import get_db_pool

# 设置日志等级为DEBUG以获取详细信息
logging.basicConfig(level=logging.DEBUG)
logger = get_logger("test_channel_stats")

def test_database_connection():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    try:
        db_pool = get_db_pool()
        conn = db_pool.get_connection()
        cursor = conn.cursor()
        
        # 执行简单查询
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        assert result[0] == 1, "数据库连接测试失败"
        
        # 检查是否可以连接到ytb_channels表
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public'
                AND table_name = 'ytb_channels'
            )
        """)
        has_channels_table = cursor.fetchone()[0]
        
        if has_channels_table:
            logger.info("ytb_channels表存在")
        else:
            logger.warning("ytb_channels表不存在，可能需要先初始化数据库")
        
        db_pool.close_connection(conn, cursor)
        logger.info("数据库连接测试成功")
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        return False

def test_ensure_database_structure():
    """测试确保数据库结构的方法"""
    logger.info("开始测试ensure_database_structure方法...")
    
    try:
        result = ChannelStatsUpdater.ensure_database_structure()
        assert result is True, "应该成功创建数据库表结构"
        logger.info("测试成功：数据库表结构创建成功")
        return True
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False

def test_get_channel_snapshots():
    """测试获取频道快照的方法"""
    # 使用一个存在的频道ID
    channel_id = "UCxxxxxxxxxxxxxxx"  # 替换为实际存在的频道ID
    
    logger.info(f"开始测试get_channel_snapshots方法，使用频道ID: {channel_id}")
    
    try:
        # 获取最近7天的快照
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, start_date, end_date)
        logger.info(f"获取到 {len(snapshots)} 条快照记录")
        
        # 打印快照数据，重点检查channel_id字段
        for i, snapshot in enumerate(snapshots[:3]):  # 只打印前3条
            logger.info(f"快照 {i+1}: ID={snapshot.get('id')}, 频道ID={snapshot.get('channel_id')}, "
                       f"时间={snapshot.get('snapshot_at')}")
        return True
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False

def main():
    """运行所有测试"""
    success = True
    
    # 首先测试数据库连接
    if not test_database_connection():
        logger.error("数据库连接测试失败，停止后续测试")
        return 1
    
    # 然后测试数据库结构确保
    if not test_ensure_database_structure():
        success = False
    
    # 暂时注释掉需要实际频道ID的测试
    # if not test_get_channel_snapshots():
    #     success = False
    
    if success:
        logger.info("所有测试通过！")
        return 0
    else:
        logger.error("测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 