import os
import sys
from pathlib import Path
# 使用psycopg2-binary而不是psycopg2
try:
    import psycopg2
except ImportError:
    print("尝试导入psycopg2-binary...")
    # 如果psycopg2导入失败，尝试导入psycopg2-binary
    import psycopg2.binary
from urllib.parse import urlparse
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 加载环境变量
load_dotenv()

def test_direct_connection():
    """直接使用psycopg2测试数据库连接"""
    try:
        print("正在测试数据库直接连接...")
        
        # 获取数据库URL
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ 错误: DATABASE_URL 环境变量未设置")
            return False
            
        print(f"数据库URL: {database_url.split('@')[0]}...@{database_url.split('@')[1]}")
        
        # 直接使用连接字符串连接
        print("尝试直接连接...")
        conn = psycopg2.connect(database_url)
        print("✅ 数据库直接连接成功!")
        
        # 查询SSL状态
        cursor = conn.cursor()
        cursor.execute("SHOW ssl")
        ssl_status = cursor.fetchone()[0]
        print(f"SSL状态: {ssl_status}")
        
        # 执行测试查询
        cursor.execute("SELECT current_timestamp, current_database(), version()")
        result = cursor.fetchone()
        
        print(f"当前时间: {result[0]}")
        print(f"数据库名称: {result[1]}")
        print(f"PostgreSQL版本: {result[2]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        print("✅ 数据库连接已关闭")
        
        return True
    except Exception as e:
        print(f"❌ 数据库直接连接失败: {str(e)}")
        return False

def test_pool_connection():
    """使用连接池测试数据库连接"""
    try:
        from src.database import get_db_pool
        
        print("\n正在测试数据库连接池...")
        # 获取数据库连接池
        db_pool = get_db_pool()
        print(f"数据库配置: {str({k: '***' if k == 'password' else v for k, v in db_pool.config.items()})}")
        
        # 执行简单查询
        result = db_pool.execute_query("SELECT current_timestamp, current_database(), version();")
        
        if result:
            print("\n✅ 数据库连接池连接成功!")
            print(f"当前时间: {result[0][0]}")
            print(f"数据库名称: {result[0][1]}")
            print(f"PostgreSQL版本: {result[0][2]}")
            return True
        else:
            print("\n❌ 查询执行成功但没有返回结果")
            return False
            
    except Exception as e:
        print(f"\n❌ 数据库连接池测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 先测试直接连接
    direct_test = test_direct_connection()
    
    # 如果直接连接成功，再测试连接池
    if direct_test:
        test_pool_connection() 