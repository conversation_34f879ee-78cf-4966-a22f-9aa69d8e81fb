import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import asyncio
from datetime import datetime, timedelta
from src.utils.channel_stats_updater import ChannelStatsUpdater
from src.utils.logger import get_logger

logger = get_logger('test_statistics')

async def test_update():
    # 获取昨天的日期
    target_date = datetime.now() - timedelta(days=1)
    logger.info(f"开始手动测试频道统计更新，目标日期: {target_date.strftime('%Y-%m-%d')}")
    
    # 执行更新
    success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
    logger.info(f"频道统计数据更新完成: {'成功' if success else '失败'}")

if __name__ == "__main__":
    asyncio.run(test_update())
