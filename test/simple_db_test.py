import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 检查是否设置了数据库URL
database_url = os.getenv('DATABASE_URL')
if not database_url:
    print("错误: 未设置DATABASE_URL环境变量")
    sys.exit(1)

print(f"数据库URL: {database_url.split('@')[0]}...@{database_url.split('@')[1]}")

try:
    print("\n尝试导入psycopg2-binary...")
    # 确保使用psycopg2-binary而不是psycopg2
    import psycopg2
    print(f"psycopg2版本: {psycopg2.__version__}")
except ImportError as e:
    print(f"导入失败: {str(e)}")
    print("请安装libpq库: brew install postgresql")
    sys.exit(1)

try:
    # 创建一个简单的数据库连接
    print("\n尝试连接数据库...")
    conn = psycopg2.connect(database_url)
    
    # 创建游标
    print("创建游标...")
    cursor = conn.cursor()
    
    # 检查SSL状态
    print("检查SSL状态...")
    cursor.execute("SHOW ssl")
    ssl_status = cursor.fetchone()[0]
    print(f"SSL状态: {ssl_status}")
    
    # 执行简单查询
    print("执行查询...")
    cursor.execute("SELECT current_timestamp, current_database(), version()")
    result = cursor.fetchone()
    
    # 输出结果
    if result:
        print("\n✅ 数据库连接成功!")
        print(f"当前时间: {result[0]}")
        print(f"数据库名称: {result[1]}")
        print(f"PostgreSQL版本: {result[2]}")
    
    # 关闭连接
    cursor.close()
    conn.close()
    print("数据库连接已关闭")
    
except Exception as e:
    print(f"\n❌ 数据库连接失败: {str(e)}")
    print("\n以下是常见解决方案:")
    print("1. 如果是libpq库问题: brew install postgresql")
    print("2. 如果是SSL问题: 确保在连接字符串中使用了sslmode=require")
    print("3. 如果是防火墙问题: 确保防火墙允许出站连接到数据库服务器")
    print("4. 如果是网络问题: 检查您的网络连接和数据库是否可达") 