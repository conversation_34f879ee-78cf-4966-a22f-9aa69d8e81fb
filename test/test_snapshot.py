import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import asyncio
from datetime import datetime, timedelta
from src.utils.channel_stats_updater import ChannelStatsUpdater
from src.utils.logger import get_logger
from src.services.channel_service import update_channel_info_snapshot
logger = get_logger('test_statistics')

async def test_update():
    # 快照数据测试，获取当日快照信息 
    update_channel_info_snapshot()

if __name__ == "__main__":
    asyncio.run(test_update())
