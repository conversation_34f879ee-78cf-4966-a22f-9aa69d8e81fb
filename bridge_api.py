#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube API的过渡版本启动脚本
采用延迟加载策略避免内存错误
"""

import os
import sys
from pathlib import Path
import uvicorn

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    # 设置默认端口
    port = 8100
    
    # 检查命令行参数是否提供了端口
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}，使用默认端口 {port}")
    
    print(f"启动 YouTube API 服务，访问地址 http://localhost:{port}")
    print("API文档地址: http://localhost:{}/docs".format(port))
    print("退出请按 Ctrl+C")
    
    # 设置环境变量，避免在Worker进程中重复初始化
    os.environ['UVICORN_WEB_CONCURRENCY'] = '1'
    os.environ['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))
    
    # 使用应用工厂函数
    app_path = "src.delayed_app:create_app"
    
    # 启动API服务，禁用热重载和多工作进程，避免内存问题
    uvicorn.run(
        app_path,
        host="0.0.0.0",
        port=port,
        reload=False,
        workers=1,
        log_level="info",
        factory=True
    ) 