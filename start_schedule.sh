#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 切换到脚本所在目录
cd "$SCRIPT_DIR"

# 确保logs目录存在
mkdir -p logs

# 查找并终止现有的 Python 进程
PID=$(pgrep -f "python3 src/main.py --schedule")
if [ ! -z "$PID" ]; then
    echo "终止现有定时任务进程，PID: $PID"
    kill $PID
    sleep 2
fi

# 设置环境变量
export PYTHONPATH=$SCRIPT_DIR:$PYTHONPATH

# 启动 Python 脚本并在后台运行，将日志输出到logs文件夹
nohup python3 src/main.py --schedule > logs/schedule_$(date +%Y%m%d).log 2>&1 &

# 输出新进程 ID
NEW_PID=$!
echo "定时任务已启动，进程 ID: $NEW_PID"

# 创建一个简单的状态检查
sleep 2
if ps -p $NEW_PID > /dev/null; then
    echo "定时任务正在运行中..."
    echo "查看日志: tail -f logs/schedule_$(date +%Y%m%d).log"
else
    echo "定时任务启动失败，请检查日志文件"
fi 