#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动YouTube API服务的脚本
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import uvicorn

if __name__ == "__main__":
    # 设置默认端口
    port = 8100
    
    # 检查命令行参数是否提供了端口
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}，使用默认端口 {port}")
    
    print(f"启动 YouTube API 服务，访问地址 http://localhost:{port}")
    print("API文档地址: http://localhost:{}/docs".format(port))
    print("退出请按 Ctrl+C")
    
    # 启动API服务
    uvicorn.run("src.fastapi_app:app", host="0.0.0.0", port=port, reload=True) 