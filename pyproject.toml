[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "reyoutube-fastapi"
version = "0.1.0"
description = "YouTube相关功能的FastAPI应用"
requires-python = ">=3.8.1"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Author", email = "<EMAIL>"}
]

dependencies = [
    # 基础依赖
    "python-dotenv==1.0.0",
    "pyyaml==6.0.1",
    "requests==2.31.0",
    "beautifulsoup4==4.12.2",
    "schedule==1.2.0",
    "pytz==2023.3",
    
    # 数据库相关
    "pymysql==1.1.0",
    "dbutils==3.0.3",
    "mysql-connector-python==8.2.0",
    "psycopg2-binary==2.9.9",
    "asyncpg==0.28.0",
    "sqlalchemy==2.0.22",
    
    # Notion API
    "notion-client==2.0.0",
    "markdown-it-py==3.0.0",
    "tenacity==8.2.3",
    
    # 图片处理
    "Pillow==10.0.1",
    
    # 中文处理
    "pypinyin==0.49.0",
    "jieba==0.42.1",
    
    # 日志和监控
    "structlog==23.1.0",
    
    # 网络和爬虫
    "aiohttp==3.9.5",
    "lxml==4.9.3",
    "fake-useragent==1.2.1",
    "httpx==0.24.0",
    "yt-dlp==2023.11.16",
    
    # FastAPI和相关依赖
    "fastapi==0.104.1",
    "uvicorn==0.24.0",
    "pydantic==2.4.2",
    "python-multipart==0.0.6",
]

[project.optional-dependencies]
dev = [
    # 开发工具
    "pytest==7.4.0",
    "black==23.7.0",
    "flake8==6.1.0",
]

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'

[tool.flake8]
max-line-length = 88
exclude = [".git", "__pycache__", "build", "dist"]

[tool.pytest.ini_options]
testpaths = ["test"] 