#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化FastAPI应用，用于测试和解决内存错误
"""

import os
import sys
from pathlib import Path
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 创建最小化FastAPI应用
app = FastAPI(
    title="YouTube API - Minimal",
    description="最小化测试版本",
    version="1.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "API正常工作"}

@app.get("/api/test")
async def test():
    return {"status": "success", "message": "测试接口正常"}

if __name__ == "__main__":
    # 设置默认端口
    port = 8100
    
    # 检查命令行参数是否提供了端口
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}，使用默认端口 {port}")
    
    print(f"启动 最小化测试 API 服务，访问地址 http://localhost:{port}")
    print("API文档地址: http://localhost:{}/docs".format(port))
    print("退出请按 Ctrl+C")
    
    # 使用单进程模式启动，不使用reload
    uvicorn.run(
        "minimal_app:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        workers=1,
        log_level="info"
    ) 