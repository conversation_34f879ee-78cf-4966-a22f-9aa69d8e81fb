import requests
from pprint import pprint
from datetime import datetime
import json
import os
import sys
from pathlib import Path
from requests.exceptions import ProxyError, RequestException
from urllib3.exceptions import MaxRetryError

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv
from src.utils.logger import get_logger

# 加载环境变量
load_dotenv()

# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")
PROXY_HOST = os.getenv("PROXY_HOST", "127.0.0.1")
PROXY_PORT = os.getenv("PROXY_PORT", "7890")
USE_PROXY = os.getenv("USE_PROXY", "false").lower() == "true"

if not YOUTUBE_API_KEY:
    print("警告：未配置YouTube API密钥，请在.env文件中设置YOUTUBE_API_KEY")

logger = get_logger('spider_youtube')

"""
1. 参考:https://blog.jiatool.com/posts/youtube_spider_api/#-%E5%8F%96%E5%BE%97-youtube-data-api-key
2. CHANELId获取：点击用户评论的头像，URL中有Id值
3. F12 元素搜索：https://www.youtube.com/channel  后面找到channeid值

https://blog.jiatool.com/
"""
def init_clients():
    """初始化数据库结构和配置"""
    try:
        # 初始化数据库表结构
        from dao.youtube_dao import YouTubeDao
        from dao.channel_dao import ChannelDao
        from dao.task_dao import TaskDao
        from dao.raw_fetch_dao import RawFetchDao
        
        # 创建所有表结构
        TaskDao.create_table_if_not_exists()
        RawFetchDao.create_table_if_not_exists()
        ChannelDao.create_table_if_not_exists()
        YouTubeDao.create_table_if_not_exists()
        
        logger.info("数据库初始化成功")
        
        # 不再初始化Notion客户端
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def process_channel(youtube_spider, channel_id):
    """处理单个YouTube频道，获取视频信息并保存到数据库
    
    Args:
        youtube_spider (YoutubeSpider): YouTube爬虫实例
        channel_id (str): 频道ID
    """
    try:
        # 导入 YouTubeDao 和 TaskDao
        from src.dao.youtube_dao import YouTubeDao
        from src.dao.task_dao import TaskDao
        from src.dao.raw_fetch_dao import RawFetchDao
        
        # 创建频道信息获取任务
        task_params = {"channel_id": channel_id, "fetch_type": "channel_info"}
        channel_task_id = TaskDao.create_task("channel_info", task_params)
        
        # 更新任务状态为运行中
        TaskDao.update_task_status(channel_task_id, "running")
        
        # 获取频道上传的播放列表ID
        uploads_id = youtube_spider.get_channel_uploads_id(channel_id)
        # uploads_id=channel_id
        
        if not uploads_id:
            error_msg = f"无法获取频道 {channel_id} 的上传播放列表ID"
            logger.error(error_msg)
            TaskDao.update_task_status(channel_task_id, "failed", error_message=error_msg)
            return
        
        # 创建获取视频列表任务
        videos_task_params = {"channel_id": channel_id, "playlist_id": uploads_id, "fetch_type": "video_list"}
        videos_task_id = TaskDao.create_task("video_list", videos_task_params)
        
        # 更新任务状态为运行中
        TaskDao.update_task_status(videos_task_id, "running")
        
        # 获取上传播放列表中的视频ID
        video_ids = youtube_spider.get_playlist(uploads_id, max_results=50)
        
        # 保存原始视频列表数据
        playlist_endpoint = f"playlistItems?part=contentDetails&playlistId={uploads_id}&maxResults=50"
        RawFetchDao.save_raw_data(
            videos_task_id, 
            playlist_endpoint, 
            {"video_ids": video_ids}, 
            {"playlist_id": uploads_id, "max_results": 50}
        )
        
        logger.info(f"获取到频道 {channel_id} 的视频列表: {len(video_ids)} 个视频")
        
        # 更新视频列表任务状态为成功
        TaskDao.update_task_status(videos_task_id, "success", result={"video_count": len(video_ids)})
        
        # 处理每个视频
        processed_videos = 0
        
        for video_id in video_ids:
            # 创建视频信息获取任务
            video_task_params = {"video_id": video_id, "channel_id": channel_id, "fetch_type": "video_info"}
            video_task_id = TaskDao.create_task("video_info", video_task_params)
            
            # 更新任务状态为运行中
            TaskDao.update_task_status(video_task_id, "running")
            
            try:
                # 获取视频详细信息
                video_info = youtube_spider.get_video(video_id)
                
                # 保存原始视频数据
                video_endpoint = f"videos?part=snippet,statistics&id={video_id}"
                RawFetchDao.save_raw_data(
                    video_task_id,
                    video_endpoint,
                    video_info.get('raw_response', {}),  # 使用完整的原始响应
                    {"video_id": video_id, "parts": "snippet,statistics"}
                )
                
                if not video_info.get('title'):
                    TaskDao.update_task_status(video_task_id, "failed", error_message="无法获取视频标题")
                    continue
                
                # 检查视频是否已存在于数据库
                existing_video = YouTubeDao.get_video_by_id(video_id)
                is_update = existing_video is not None
                
                # 保存或更新到数据库
                YouTubeDao.save_video(video_info)
                
                if is_update:
                    logger.info(f"更新视频: {video_info['title']}")
                    TaskDao.update_task_status(video_task_id, "success", result={"status": "updated"})
                else:
                    logger.info(f"保存新视频: {video_info['title']}")
                    processed_videos += 1
                    # 更新视频任务状态为成功
                    TaskDao.update_task_status(video_task_id, "success", result={
                        "title": video_info['title']
                    })
            
            except Exception as e:
                logger.error(f"处理视频 {video_id} 失败: {str(e)}")
                TaskDao.update_task_status(video_task_id, "failed", error_message=str(e))
        
        # 更新频道任务状态为成功
        TaskDao.update_task_status(channel_task_id, "success", result={
            "videos_processed": processed_videos,
            "total_videos": len(video_ids)
        })
            
        logger.info(f'频道 {channel_id} 处理完成')
        
    except Exception as e:
        logger.error(f'处理频道 {channel_id} 失败: {str(e)}')
        # 更新任务状态为失败
        try:
            TaskDao.update_task_status(channel_task_id, "failed", error_message=str(e))
        except:
            pass  # 如果channel_task_id未定义，忽略错误

def channel_ids_read_file():
    """读取YouTube频道ID配置文件"""
    data_dir = 'data'
    os.makedirs(data_dir, exist_ok=True)
    file_path = os.path.join(data_dir, "channel_ids.json")
    
    with open(file_path, 'r') as f:
        # 读取json数据
        ids = json.load(f)
        return ids


class YoutubeSpider():
    def __init__(self, api_key):
        self.base_url = "https://www.googleapis.com/youtube/v3/"
        self.api_key = api_key
        self.session = requests.Session()
        
        # 配置代理
        if USE_PROXY:
            self.proxies = {
                'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
                'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
            }
            logger.info(f"使用代理: {self.proxies['http']}")
        else:
            self.proxies = None
            logger.info("不使用代理")

    def get_html_to_json(self, path):
        """发送API请求并处理响应"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                api_url = f"{self.base_url}{path}&key={self.api_key}"
                
                # 发送请求，如果配置了代理则使用代理
                response = self.session.get(
                    api_url,
                    proxies=self.proxies,
                    timeout=10  # 设置超时时间
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 403:
                    logger.error(f"API密钥无效或超出配额: {response.text}")
                    return None
                else:
                    logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                    return None
                    
            except ProxyError as e:
                logger.error(f"代理连接失败 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1
                if retry_count == max_retries:
                    logger.error("代理连接多次失败，请检查代理配置")
                    return None
                    
            except RequestException as e:
                logger.error(f"请求异常 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1
                if retry_count == max_retries:
                    return None
                    
            except Exception as e:
                logger.error(f"未知错误: {str(e)}")
                return None

    def get_channel_uploads_id(self, channel_id, part='contentDetails'):
        """取得頻道上傳影片清單的ID"""
        # UC7ia-A8gma8qcdC6GDcjwsQ
        path = f'channels?part={part}&id={channel_id}'
        data = self.get_html_to_json(path)
        if data is None:
            return channel_id
        try:
            uploads_id = data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
        except KeyError:
            uploads_id = None
        return uploads_id

    def get_playlist(self, playlist_id, part='contentDetails', max_results=10):
        """取得影片清單ID中的影片"""
        # UU7ia-A8gma8qcdC6GDcjwsQ
        path = f'playlistItems?part={part}&playlistId={playlist_id}&maxResults={max_results}'
        data = self.get_html_to_json(path)
        if not data:
            return []

        video_ids = []
        for data_item in data['items']:
            video_ids.append(data_item['contentDetails']['videoId'])
        return video_ids

    def get_video(self, video_id, part='snippet,statistics'):
        """取得影片資訊"""
        # jyordOSr4cI
        # part = 'contentDetails,id,liveStreamingDetails,localizations,player,recordingDetails,snippet,statistics,status,topicDetails'
        path = f'videos?part={part}&id={video_id}'
        data = self.get_html_to_json(path)
        if not data:
            return {}
        
        # 返回完整的API响应数据
        if 'items' in data and len(data['items']) > 0:
            data_item = data['items'][0]
            
            try:
                # 2019-09-29T04:17:05Z
                time_ = datetime.strptime(data_item['snippet']['publishedAt'], '%Y-%m-%dT%H:%M:%SZ')
                formatted_time = time_.strftime('%Y-%m-%d %H:%M:%S')
            except ValueError:
                # 日期格式錯誤
                time_ = None
                formatted_time = None
            
            url_ = f"https://www.youtube.com/watch?v={data_item['id']}"
            
            # 提取缩略图信息
            thumbnails = data_item['snippet'].get('thumbnails', {})
            
            # 优先使用maxres质量的缩略图，如果不存在则依次降级尝试其他质量
            thumbnail_url = None
            for quality in ['maxres', 'standard', 'high', 'medium', 'default']:
                if quality in thumbnails and 'url' in thumbnails[quality]:
                    thumbnail_url = thumbnails[quality]['url']
                    # 找到后立即跳出循环
                    if quality == 'maxres':
                        break
            
            # 提取标签信息
            tags = data_item['snippet'].get('tags', [])
            
            # 构建视频信息，包含所有原始字段
            info = {
                # 原始字段
                'kind': data.get('kind'),
                'etag': data.get('etag'),
                'pageInfo': data.get('pageInfo'),
                
                # 提取字段（保持原有逻辑）
                'id': data_item['id'],
                'channelId': data_item['snippet']['channelId'],  # 使用YouTube channel ID
                'channelTitle': data_item['snippet']['channelTitle'],
                'publishedAt': formatted_time,
                'video_url': url_,
                'title': data_item['snippet']['title'],
                'description': data_item['snippet']['description'],
                'likeCount': data_item['statistics'].get('likeCount', 0),
                'commentCount': data_item['statistics'].get('commentCount', 0),
                'viewCount': data_item['statistics'].get('viewCount', 0),
                
                # 添加标签和缩略图信息
                'tags': tags,
                'thumbnails': thumbnails,
                # 使用优先选择的缩略图URL
                'thumbnailUrl': thumbnail_url,
                
                # 保存原始响应
                'raw_response': data
            }
            return info
        return {}

    def get_comments(self, video_id, page_token='', part='snippet', max_results=100):
        """取得影片留言"""
        # jyordOSr4cI
        path = f'commentThreads?part={part}&videoId={video_id}&maxResults={max_results}&pageToken={page_token}'
        data = self.get_html_to_json(path)
        if not data:
            return [], ''
        # 下一頁的數值
        next_page_token = data.get('nextPageToken', '')

        # 以下整理並提取需要的資料
        comments = []
        for data_item in data['items']:
            data_item = data_item['snippet']
            top_comment = data_item['topLevelComment']
            try:
                # 2020-08-03T16:00:56Z
                time_ = datetime.strptime(top_comment['snippet']['publishedAt'], '%Y-%m-%dT%H:%M:%SZ')
            except ValueError:
                # 日期格式錯誤
                time_ = None

            if 'authorChannelId' in top_comment['snippet']:
                ru_id = top_comment['snippet']['authorChannelId']['value']
            else:
                ru_id = ''

            ru_name = top_comment['snippet'].get('authorDisplayName', '')
            if not ru_name:
                ru_name = ''

            comments.append({
                'reply_id': top_comment['id'],
                'ru_id': ru_id,
                'ru_name': ru_name,
                'reply_time': time_,
                'reply_content': top_comment['snippet']['textOriginal'],
                'rm_positive': int(top_comment['snippet']['likeCount']),
                'rn_comment': int(data_item['totalReplyCount'])
            })
        return comments, next_page_token


def youtube_main():
    """主函数入口，初始化数据库并处理所有配置的YouTube频道"""
    try:
        # 初始化数据库和配置
        init_clients()
        
        # 导入ChannelDao
        from dao.channel_dao import ChannelDao
        
        # 初始化YouTube爬虫
        youtube_spider = YoutubeSpider(YOUTUBE_API_KEY)
        
        # 直接从数据库获取所有频道
        channels = ChannelDao.get_all_channels()
        logger.info(f"从数据库获取到 {len(channels)} 个频道")
        
        if not channels:
            logger.warning("数据库中没有频道数据，请先添加频道")
            return
        
        # 处理每个频道的视频
        for channel in channels:
            # 更新频道信息
            # channel_info = get_channel_info(channel_id)
            channel_id = channel['channel_id']
            channel_title = channel['title']
            
            logger.info(f"开始处理频道: {channel_title} (ID: {channel_id})")
            
            # 直接处理频道视频，跳过频道信息获取步骤
            process_channel(youtube_spider, channel_id)
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise

def get_channel_info(channel_id):
    """
    获取YouTube频道信息并保存到数据库
    
    Args:
        channel_id (str): YouTube频道ID
        
    Returns:
        dict: 包含频道信息的字典,包括:
            - id: 频道ID
            - title: 频道标题
            - description: 频道描述
            - publishedAt: 频道创建时间
            - viewCount: 总观看数
            - subscriberCount: 订阅数
            - videoCount: 视频数量
    """
    try:
        # 导入所需的DAO
        from dao.channel_dao import ChannelDao
        from dao.task_dao import TaskDao
        from dao.raw_fetch_dao import RawFetchDao
        
        # 创建频道信息获取任务
        task_params = {"channel_id": channel_id, "fetch_type": "channel_info"}
        task_id = TaskDao.create_task("channel_info", task_params)
        
        # 更新任务状态为运行中
        TaskDao.update_task_status(task_id, "running")
        
        # 构建API请求URL
        url = f"https://www.googleapis.com/youtube/v3/channels"
        params = {
            'key': YOUTUBE_API_KEY,
            'id': channel_id,
            'part': 'snippet,statistics,brandingSettings'
        }
        
        # 发送请求
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        
        # 保存原始数据
        endpoint = f"channels?part=snippet,statistics,brandingSettings&id={channel_id}"
        RawFetchDao.save_raw_data(
            task_id,
            endpoint,
            data,
            params
        )
        
        if not data['items']:
            error_msg = f'未找到频道信息: {channel_id}'
            logger.warning(error_msg)
            TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
            return None
            
        channel = data['items'][0]
        
        # 获取品牌设置中的封面图
        banner_url = None
        if 'brandingSettings' in channel and 'image' in channel['brandingSettings']:
            banner_url = channel['brandingSettings']['image'].get('bannerExternalUrl', None)
        
        # 获取缩略图
        thumbnail_url = None
        if 'snippet' in channel and 'thumbnails' in channel['snippet']:
            thumbnails = channel['snippet']['thumbnails']
            for quality in ['high', 'medium', 'default']:
                if quality in thumbnails:
                    thumbnail_url = thumbnails[quality]['url']
                    break
        
        channel_info = {
            'id': channel['id'],
            'title': channel['snippet']['title'],
            'description': channel['snippet']['description'],
            'customUrl': channel['snippet'].get('customUrl', ''),
            'publishedAt': channel['snippet']['publishedAt'],
            'viewCount': int(channel['statistics'].get('viewCount', 0)),
            'subscriberCount': int(channel['statistics'].get('subscriberCount', 0)),
            'videoCount': int(channel['statistics'].get('videoCount', 0)),
            'country': channel['snippet'].get('country', ''),
            'thumbnailUrl': thumbnail_url,
            'bannerUrl': banner_url,
            'isVerified': False  # 这个信息API无法直接获取
        }
        
        # 保存到数据库
        try:
            # 保存到数据库
            ChannelDao.save_channel(channel_info)
            logger.info(f'成功将频道信息保存到数据库: {channel_info["title"]}')
            
            # 更新任务状态为成功
            TaskDao.update_task_status(task_id, "success", result={
                "title": channel_info["title"],
                "subscriberCount": channel_info["subscriberCount"]
            })
            
            return channel_info
            
        except Exception as e:
            logger.error(f"保存频道信息到数据库失败: {str(e)}")
            TaskDao.update_task_status(task_id, "failed", error_message=f"保存频道信息失败: {str(e)}")
            return channel_info
            
    except Exception as e:
        logger.error(f"获取频道信息失败: {str(e)}")
        # 尝试更新任务状态
        try:
            TaskDao.update_task_status(task_id, "failed", error_message=str(e))
        except:
            pass  # 如果task_id未定义，忽略错误
        return None


if __name__ == "__main__":
    # youtube_main()
    get_channel_info('UCUDwjhlMW8nJLeBSInhPFBQ')
    # notion_main()
