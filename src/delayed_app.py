#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用应用工厂模式的延迟加载FastAPI应用
避免内存分配错误
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os
import sys
import traceback
from dotenv import load_dotenv
import logging

# 配置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("delayed_app")

# 加载环境变量
load_dotenv()

def create_app():
    """
    创建并配置FastAPI应用
    使用延迟导入避免内存问题
    """
    # 创建FastAPI应用
    app = FastAPI(
        title="YouTube API",
        description="用于查询YouTube频道信息的API（延迟加载版）",
        version="1.0.0",
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # 默认路由
    @app.get("/")
    async def root():
        return {"message": "YouTube API服务已启动（延迟加载版）"}
    
    # 测试路由
    @app.get("/api/test")
    async def test():
        return {"status": "success", "message": "API测试成功"}
    
    # 健康检查路由
    @app.get("/health")
    async def health():
        """健康检查接口"""
        return {"status": "healthy", "version": "1.0.0"}
    
    # 应用启动事件
    @app.on_event("startup")
    async def startup_event():
        logger.info("应用启动中...")
        try:
            # 这里可以添加数据库初始化等代码
            # 但暂时先不添加，确定基本应用能启动
            logger.info("应用初始化成功")
        except Exception as e:
            logger.error(f"应用初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
    
    # 应用关闭事件
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("应用正在关闭...")
        try:
            # 这里可以添加资源清理代码
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {str(e)}")
    
    # 返回应用实例
    return app 