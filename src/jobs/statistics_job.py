import asyncio
from datetime import datetime, timedelta
from ..utils.channel_stats_updater import ChannelStatsUpdater
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('statistics_job')

async def schedule_daily_statistics_update():
    """
    调度频道统计数据每日更新任务
    每天凌晨 2:00 运行，处理前一天的数据
    """
    while True:
        now = datetime.now()
        
        # 计算到下一个凌晨 2:00 的秒数
        target_time = now.replace(hour=2, minute=0, second=0, microsecond=0)
        if now.hour >= 2:
            target_time = target_time + timedelta(days=1)
        
        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()
        
        logger.info(f"statistics_job：频道统计数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")
        
        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)
        
        # 运行统计数据更新
        try:
            logger.info("开始执行频道统计数据每日更新")
            # 处理前一天的数据
            target_date = datetime.now() - timedelta(days=1)
            success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
            logger.info(f"频道统计数据更新完成: {'成功' if success else '失败'}")
        except Exception as e:
            logger.error(f"频道统计数据更新出错: {str(e)}")
        
        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

async def start():
    """启动统计数据更新任务"""
    logger.info("启动频道统计数据每日更新任务")
    asyncio.create_task(schedule_daily_statistics_update()) 