import os
import shutil
import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from ..utils.download_utils import TEMP_DIR

# 设置日志
logger = get_logger('cleanup_job')

async def cleanup_old_downloads():
    """
    清理旧的下载文件，删除1小时前的临时下载文件夹
    """
    while True:
        try:
            now = datetime.now()
            cutoff = now - timedelta(hours=1)  # 删除1小时前的文件
            
            for item in os.listdir(TEMP_DIR):
                item_path = os.path.join(TEMP_DIR, item)
                if os.path.isdir(item_path):
                    created_time = datetime.fromtimestamp(os.path.getctime(item_path))
                    if created_time < cutoff:
                        shutil.rmtree(item_path)
                        logger.info(f"已清理过期下载目录: {item_path}")
        except Exception as e:
            logger.error(f"清理下载目录失败: {str(e)}")
        
        await asyncio.sleep(3600)  # 每小时运行一次

async def start():
    """启动清理任务"""
    logger.info("启动临时文件清理任务")
    asyncio.create_task(cleanup_old_downloads()) 