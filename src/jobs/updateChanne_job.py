import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from src.services.channel_service import update_channel_info_daily,update_channel_info_snapshot
# 设置日志
logger = get_logger('update_channel_job')

async def update_channel_job():
    """
    API方式-频道快照数据更新: 更新频道最新的订阅数量，浏览数量信息，每日更新，更新一次，每日零点十分触发更新
    """
    while True:
        now = datetime.now()
        
        # 计算到下一个凌晨 0:10 的秒数
        target_time = now.replace(hour=0, minute=10, second=0, microsecond=0)
        if now.hour >= 0:
            target_time = target_time + timedelta(days=1)
        
        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()
        
        logger.info(f"updateChanne_job：频道数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")
        
        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)
        
        # 运行统计数据更新
        try:
            logger.info("开始执行频道数据每日更新")
            success=update_channel_info_snapshot()
            # success=update_channel_info_daily()
            logger.info(f"频道统计数据更新完成: {'成功' if success else '失败'}")
        except Exception as e:
            logger.error(f"频道统计数据更新出错: {str(e)}")
        
        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

    

async def start():
    """启动清理任务"""
    logger.info("启动更新频道信息")
    asyncio.create_task(update_channel_job()) 
    