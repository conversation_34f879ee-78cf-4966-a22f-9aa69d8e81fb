"""Jobs package for background tasks."""
import asyncio
import schedule
import time
from . import cleanup_job, statistics_job, task_scheduler, update<PERSON>hanne_job,updateVideos_job,updateChannelAll_job
# from .updateVideos_job import update_videos_job, logger as update_videos_logger # 导入任务和日志记录器

# 辅助函数，用于在 asyncio 事件循环中运行异步任务
def run_async_job(job_func):
    asyncio.create_task(job_func())

# async def run_scheduled_tasks():
#     """运行所有通过 schedule 库安排的定时任务"""
#     # 安排 update_videos_job 每天 00:00:01 执行
#     schedule.every().day.at("00:00:01").do(run_async_job, update_videos_job)
#     update_videos_logger.info("已安排 'update_videos_job' 任务每日 00:00:01 执行")

#     while True:
#         schedule.run_pending()
#         await asyncio.sleep(1) # 每秒检查一次是否有待处理的任务

async def start_all():
    """启动所有后台任务，包括定时任务调度器"""
    await asyncio.gather(
        # cleanup_job.start(),
        task_scheduler.start(),
        statistics_job.start(),
        updateChanne_job.start(),
        updateVideos_job.start(),
        updateChannelAll_job.start()
    ) 