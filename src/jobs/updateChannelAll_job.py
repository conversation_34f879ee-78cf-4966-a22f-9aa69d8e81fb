import os
import shutil
import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from ..utils.download_utils import TEMP_DIR
from ..spider.spider_youtube import youtube_main
from src.services.yt_dlp_all_channel import main as yt_dlp_all_channel_main
from src.services.yt_dlp_all_videos import main as yt_dlp_all_videos_main
# 设置日志
logger = get_logger('updateChannelAll_job')

async def update_channel_all_job():
    """
    yt-dlp方式: 更新所有频道历史数据-同时更新频道的视频数据
    """
    while True:
        now = datetime.now()
        
        # 计算到下一个凌晨 0:10 的秒数
        target_time = now.replace(hour=3, minute=0, second=0, microsecond=0)
        if now.hour >= 0:
            target_time = target_time + timedelta(days=1)
        
        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()
        
        logger.info(f"updateChannelAll_job：频道数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")
        
        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)
        
        # 运行统计数据更新
        try:
            logger.info("开始执行频道数据每日更新")
            # 更新所有频道历史数据
            yt_dlp_all_channel_main()
            logger.info(f"频道历史数据更新完成")
            # 更新所有频道视频数据
            yt_dlp_all_videos_main()
            logger.info(f"频道视频数据更新完成")
            
        except Exception as e:
            logger.error(f"频道数据更新出错: {str(e)}")
        
        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

    

async def start():
    """启动清理任务"""
    logger.info("启动更新频道的视频数据")
    asyncio.create_task(update_channel_all_job()) 