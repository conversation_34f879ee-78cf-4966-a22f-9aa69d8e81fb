import yt_dlp
import logging
import os
from flask import Flask
from flask import request
from flask_cors import CORS
import requests
import json
import threading
from functools import partial
import time
import subprocess
import shlex
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor, TimeoutError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources=r'/*')

# Update cookie configuration
# COOKIES_URL = 'https://img.aistak.com/cookie.txt'
COOKIES_URL = 'https://img.aistak.com/cookies-all.txt'
COOKIES_FILE = '/tmp/yt_cookies.txt'

def get_remote_cookies():
    """
    Fetch cookies from remote URL
    """
    try:
        response = requests.get(COOKIES_URL)
        response.raise_for_status()
        return response.text
    except Exception as e:
        logger.error(f"Failed to fetch remote cookies: {str(e)}")
        return None

def get_yt_dlp_opts(additional_opts=None):
    """
    获取yt-dlp的基础配置
    """
    opts = {
        'quiet': True,
        'no_warnings': True
    }
    
    # Try local cookies file first
    if os.path.exists(COOKIES_FILE):
        logger.info(f"Using local cookies file: {COOKIES_FILE}")
        opts['cookiefile'] = COOKIES_FILE
        
    # Fallback to remote cookies if local file doesn't exist
    else:
        cookies_content = get_remote_cookies()
        if cookies_content:
            # Write to temporary file with proper formatting
            tmp_cookie_file = '/tmp/yt_cookies.txt'
            try:
                with open(tmp_cookie_file, 'w') as f:
                    # Add required header
                    f.write("# Netscape HTTP Cookie File\n")
                    
                    # Process and write each cookie line
                    for line in cookies_content.splitlines():
                        if line.strip():
                            # Remove HttpOnly prefix if present
                            line = line.replace('#HttpOnly_', '')
                            # Convert spaces to tabs between fields
                            fields = line.split()
                            if len(fields) >= 7:
                                formatted_line = '\t'.join(fields)
                                f.write(formatted_line + '\n')
                    
                opts['cookiefile'] = tmp_cookie_file
                logger.info("Using remote cookies")
                
            except Exception as e:
                logger.error(f"Failed to process cookies: {str(e)}")
                if os.path.exists(tmp_cookie_file):
                    os.remove(tmp_cookie_file)
        else:
            logger.warning("No cookies available")
    
    # 合并额外的配置选项
    if additional_opts:
        opts.update(additional_opts)
        
    return opts

# 获取视频信息
@app.route('/ytb/info', methods=['POST'])
def get_info():
    try:
        json_data = request.get_json()
        watch_url = json_data['watch_url']
        logger.info(f"Received info request for URL: {watch_url}")

        if watch_url is None or len(watch_url) == 0:
            logger.error("Empty URL received")
            return {
                "status": "error",
                "message": "URL cannot be empty"
            }, 400

        # Extract just the video ID by splitting on v= and taking everything before any &
        watch_id = extract_video_id(watch_url)
        json_filename = f'/tmp/{watch_id}.info.json'
        
        # If file exists, return its contents
        if os.path.exists(json_filename):
            logger.info(f"Found existing info file: {json_filename}")
            try:
                with open(json_filename, 'r') as f:
                    info = json.load(f)
                    response = {
                        "status": "success",
                        "data": info,
                        "message": "success"
                    }
                    logger.info("Successfully loaded cached video information")
                    return response, 200
            except Exception as e:
                logger.error(f"Failed to read existing info file: {str(e)}")
                # Continue with normal extraction if file read fails
        
        # Proceed with normal extraction if no file exists
        # 使用统一的配置
        ydl_opts = get_yt_dlp_opts()
        
        # Start timing
        start_time = time.time()
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            logger.info("Extracting video information...")
            data = ydl.extract_info(watch_url, download=False)
            
            # Log the time taken
            elapsed_time = time.time() - start_time
            logger.info(f"Extraction took {elapsed_time:.2f} seconds")
            
            logger.info(f"Successfully extracted info for video ID: {data.get('id')}")
            
            # Group formats by resolution and pick best version
            resolution_formats = {}
            for f in data.get('formats', []):
                resolution = f.get('resolution')
                if not resolution:  # Skip entries without resolution
                    continue
                    
                # If resolution not in dict or new format is better
                if resolution not in resolution_formats or _is_better_format(f, resolution_formats[resolution]):
                    resolution_formats[resolution] = f
                    
            # Convert back to list and sort by resolution
            filtered_formats = list(resolution_formats.values())
            filtered_formats.sort(key=lambda x: _get_resolution_value(x.get('resolution', '')), reverse=True)
            
            info = {
                'id': data.get('id'),
                'title': data.get('title'),
                # 'description': data.get('description'),
                'thumbnail': data.get('thumbnail'),
                # 'duration': data.get('duration'),
                'view_count': data.get('view_count'),
                # 'like_count': data.get('like_count'),
                'channel': data.get('channel'),
                'channel_id': data.get('channel_id'),
                'upload_date': data.get('upload_date'),
                # 'categories': data.get('categories', []),
                # 'tags': data.get('tags', []),
                'formats': [{
                    # 'format_id': f.get('format_id'),
                    'ext': f.get('ext'),
                    'resolution': _get_resolution_name(f.get('resolution')),
                    'filesize': f.get('filesize'),
                    # 'tbr': f.get('tbr'),
                    'protocol': f.get('protocol'),
                    'vcodec': f.get('vcodec'),
                    'acodec': f.get('acodec'),
                    'url': f.get('url')
                } for f in filtered_formats]
            }

            # Create JSON filename
            json_filename = f'/tmp/{watch_id}.info.json'
            # Start background thread to save file
            save_thread = threading.Thread(
                target=async_save_info,
                args=(json_filename, info, logger),
                daemon=True
            )
            save_thread.start()
            
            response = {
                "status": "success",
                "data": info,
                "message": "success"
            }
            logger.info("Successfully processed video information")
            return response, 200

    except Exception as e:
        logger.error(f"Error processing info request: {str(e)}")
        response = {
            "status": "error",
            "message": str(e)
        }
        return response, 400


# 01-请求数据-json文件
@app.route('/ytb/ajax', methods=['POST'])
def ajax():
    try:
        json_data = request.get_json()
        watch_url = json_data['watch_url']
        if watch_url is None or len(watch_url) == 0:
            return 'No url', 400
        # result = ytdl_main(watch_url)
        json_file_path = get_information(watch_url)
        result=video_json_parse(json_file_path)
        response = {
            "status": "success",
            "data": result,
            "message": "success"
        }
        return response, 200
    except Exception as e:
        logger.error(f"Error in ajax request: {str(e)}")
        response = {
            "status": "error",
            "data": None,
            "message": str(e)
        }
        return response, 400

import os,json
# 解视频的json文保存
def get_information(watch_url):
    # URL = 'https://www.youtube.com/watch?v=BaW_jenozKc'
    watch_id = extract_video_id(watch_url)
    URL = watch_url
    # ℹ️ See help(yt_dlp.YoutubeDL) for a list of available options and public functions
    # 使用统一的配置
    ydl_opts = get_yt_dlp_opts()
    
    file_name = f'{watch_id}.video.info.json'
    # INFO_FILE = os.path.join(os.path.dirname(__file__), "data", file_name)
    INFO_FILE = f'/tmp/{file_name}'
    # 如果文件存在
    if os.path.exists(INFO_FILE):
        return INFO_FILE

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(URL, download=False)
        # 写入文件
        with open(INFO_FILE, 'w') as f:
            json.dump(ydl.sanitize_info(info), f, indent=4)
            return INFO_FILE

def video_json_parse(file_path):
    # file_path=os.path.join(os.path.dirname(__file__), 'video.info.json')
    with open(file_path, 'r') as f:
        data = json.load(f)
        title = data['title']
        # 封面
        thumbnails = data['thumbnails']
        for thumbnail in thumbnails:
            if thumbnail['preference'] == -1:
                cover = thumbnail['url']
        # print(thumbnails)
        # 视频时长
        duration = data['duration']
        formats = data['formats']
        videos = []
        mp3s = []
        for index, format in enumerate(formats):
            if format['video_ext'] == 'mp4':
                if format.get('filesize') is None:
                    continue
                filesize = format['filesize']
                if filesize is None:
                    continue
                resolution = format['resolution']
                filesize = bytes_to_MB(filesize)
                download = format['url']
                video = {
                    "resolution": resolution,
                    "filesize": filesize,
                    "download": download
                },
                videos.append(video)

            if format['audio_ext'] == 'm4a':
                if format.get('filesize') is None:
                    continue
                filesize = format['filesize']
                if filesize is None:
                    continue
                filesize = bytes_to_MB(filesize)
                resolution = format['resolution']
                download = format['url']
                mp3 = {
                    "resolution": resolution,
                    "filesize": filesize,
                    "download": download
                },
                mp3s.append(mp3)
        result = {
            "title": title,
            "cover": cover,
            "duration": int_to_time(duration),
            "video": videos,
            "mp3": mp3s
        }
        return result

# 将字节数转换为MB，并保留两位小数
def bytes_to_MB(size_in_bytes):
    # 1 MB = 1024 KB
    size_in_kb = size_in_bytes / 1024.0

    if size_in_kb < 1024:
        # 如果小于1MB，以KB形式显示
        return f"{size_in_kb:.2f} KB"
    else:
        # 否则以MB形式显示
        size_in_mb = size_in_kb / 1024.0
        return f"{size_in_mb:.2f} MB"

# 将整数转换为时分秒
def int_to_time(seconds):
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


# 获取视频的直链地址
@app.route('/ytb/parse', methods=['POST'])
def parse():
    try:
        json_data = request.get_json()
        watch_url = json_data['watch_url']
        logger.info(f"Received parse request for URL: {watch_url}")

        if watch_url is None or len(watch_url) == 0:
            logger.error("Empty URL received")
            return {
                "status": "error",
                "message": "URL cannot be empty"
            }, 400

        # 使用统一的配置，添加format选项
        ydl_opts = get_yt_dlp_opts({
            'format': 'bestvideo+bestaudio'
        })

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            logger.info("Extracting video formats...")
            info = ydl.extract_info(watch_url, download=False)
            formats = info.get('formats', [])
            logger.info(f"Found {len(formats)} available formats")
            
            # Get best video and audio URLs
            video_url = None 
            audio_url = None
            
            for f in formats:
                if f.get('vcodec', 'none') != 'none' and f.get('acodec', 'none') == 'none':
                    # Video only format
                    if not video_url or f.get('tbr', 0) > formats[video_url].get('tbr', 0):
                        video_url = formats.index(f)
                        
                if f.get('acodec', 'none') != 'none' and f.get('vcodec', 'none') == 'none':
                    # Audio only format
                    if not audio_url or f.get('tbr', 0) > formats[audio_url].get('tbr', 0):
                        audio_url = formats.index(f)

            response = {
                "status": "success",
                "video_url": formats[video_url]['url'] if video_url is not None else None,
                "audio_url": formats[audio_url]['url'] if audio_url is not None else None,
                "message": "success"
            }
            logger.info("Successfully extracted video and audio URLs")
            return response, 200

    except Exception as e:
        logger.error(f"Error processing parse request: {str(e)}")
        response = {
            "status": "error", 
            "message": str(e)
        }
        return response, 400


# 测试验证
@app.route('/')
def hello_world():
    return 'Hello from Flask Github!'

@app.route('/post', methods=['POST'])
def handle_request():
    return 'Hello Post'

# Helper functions
def _is_better_format(new_format, existing_format):
    """Compare two formats and return True if new format is preferred"""
    # Prefer formats with audio
    if new_format.get('acodec') != 'none' and existing_format.get('acodec') == 'none':
        return True
    # If audio status is same, prefer MP4
    if (new_format.get('acodec') == 'none') == (existing_format.get('acodec') == 'none'):
        if new_format.get('ext') == 'mp4' and existing_format.get('ext') != 'mp4':
            return True
    return False

def _get_resolution_name(resolution):
    """Convert resolution string to standard format name"""
    try:
        if not resolution or 'x' not in resolution:
            return resolution
            
        width, height = map(int, resolution.split('x'))
        
        # Map to standard resolutions based on height
        if height >= 2160:
            return '4K'
        elif height >= 1440:
            return '2K'
        elif height >= 1080:
            return '1080P'
        elif height >= 720:
            return '720P'
        elif height >= 480:
            return '480P'
        elif height >= 360:
            return '360P'
        elif height >= 240:
            return '240P'
        elif height >= 144:
            return '144P'
        else:
            return f'{height}P'  # Fallback for non-standard resolutions
            
    except:
        return resolution  # Return original if parsing fails

def _get_resolution_value(resolution):
    """Convert resolution string to numeric value for sorting"""
    try:
        if 'x' in resolution:
            return int(resolution.split('x')[1])  # Sort by height
        return 0
    except:
        return 0

def extract_video_id(watch_url):
    if "youtu.be" in watch_url:
        # For youtu.be URLs, the video ID is the last part of the path
        return watch_url.split("/")[-1]
    else:
        # For standard YouTube URLs, extract the video ID after "v="
        return watch_url.split("v=")[-1].split("&")[0]

def async_save_info(json_filename, info, logger):
    """Background task to save info to JSON file"""
    try:
        with open(json_filename, 'w') as f:
            json.dump(info, f, indent=4)
        logger.info(f"Successfully saved info to {json_filename}")
    except Exception as e:
        logger.error(f"Failed to save info to file: {str(e)}")



@app.route('/ytb/fast/info', methods=['POST'])
def get_fast_video_info():
    start_time = time.time()
    try:
        json_data = request.get_json()
        watch_url = json_data['watch_url']
        logger.info(f"[{time.time() - start_time:.2f}s] Received info request for URL: {watch_url}")

        # 提取视频ID并检查缓存
        watch_id = extract_video_id(watch_url)
        json_filename = f'/tmp/{watch_id}.info.json'
        
        # 检查缓存是否存在且未过期（24小时）
        if os.path.exists(json_filename):
            file_age = time.time() - os.path.getmtime(json_filename)
            if file_age < 24 * 3600:  # 24小时内的缓存有效
                try:
                    with open(json_filename, 'r') as f:
                        cached_data = json.load(f)
                        logger.info(f"[{time.time() - start_time:.2f}s] Returned from cache")
                        return {"status": "success", "data": cached_data, "message": "success"}, 200
                except Exception as e:
                    logger.warning(f"[{time.time() - start_time:.2f}s] Cache read failed: {str(e)}")

        # 使用线程池执行命令，设置超时
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(execute_yt_dlp_command, watch_url, start_time)
            try:
                data = future.result(timeout=15)  # 15秒超时
            except TimeoutError:
                logger.error(f"[{time.time() - start_time:.2f}s] Command execution timed out")
                return {"status": "error", "message": "Request timed out"}, 408

        # 处理格式信息
        process_start_time = time.time()
        formats = process_formats(data.get('formats', []))
        logger.info(f"[{time.time() - start_time:.2f}s] Format processing completed in {time.time() - process_start_time:.2f}s")

        # 构建返回数据
        result = build_response_data(data, formats)
        
        # 异步保存缓存
        threading.Thread(
            target=async_save_cache,
            args=(json_filename, result, logger, start_time),
            daemon=True
        ).start()

        total_time = time.time() - start_time
        logger.info(f"[{total_time:.2f}s] Request completed successfully. Total time: {total_time:.2f}s")
        return {"status": "success", "data": result, "message": "success"}, 200

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"[{total_time:.2f}s] Error in get_fast_video_info: {str(e)}")
        return {"status": "error", "message": str(e)}, 500

def execute_yt_dlp_command(watch_url, start_time, max_retries=2):
    """执行 yt-dlp 命令并返回结果"""
    retry_count = 0
    while retry_count <= max_retries:
        try:
            cmd = f'yt-dlp --dump-json --no-warning --quiet --no-playlist ' \
                  f'--no-check-certificates --no-call-home ' \
                  f'--youtube-skip-dash-manifest --youtube-skip-hls-manifest ' \
                  f'--cookies {COOKIES_FILE} ' \
                  f'{shlex.quote(watch_url)}'
            
            logger.info(f"[{time.time() - start_time:.2f}s] Executing command (attempt {retry_count + 1})")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                text=True
            )
            
            stdout, stderr = process.communicate(timeout=10)  # 10秒超时
            
            if process.returncode == 0:
                return json.loads(stdout)
            
            logger.error(f"[{time.time() - start_time:.2f}s] Command failed (attempt {retry_count + 1}): {stderr}")
            retry_count += 1
            time.sleep(1)  # 重试前等待1秒
            
        except subprocess.TimeoutExpired:
            logger.error(f"[{time.time() - start_time:.2f}s] Command timeout (attempt {retry_count + 1})")
            retry_count += 1
            time.sleep(1)
            
    raise Exception("Failed to execute yt-dlp command after multiple attempts")

def process_formats(formats):
    """处理视频格式信息，优化排序和过滤逻辑"""
    # 过滤掉没有文件大小的格式
    valid_formats = [f for f in formats if f.get('filesize')]
    
    # 按分辨率、容器格式和文件大小排序
    def format_sort_key(f):
        resolution = _get_resolution_value(f.get('resolution', ''))
        is_mp4 = 1 if f.get('ext') == 'mp4' else 0
        filesize = f.get('filesize', 0)
        return (resolution, is_mp4, filesize)
    
    sorted_formats = sorted(valid_formats, key=format_sort_key, reverse=True)
    
    # 过滤相同分辨率的非mp4格式，以及低于360p的MP4视频
    filtered_formats = []
    seen_resolutions = set()
    
    for f in sorted_formats:
        resolution = f.get('resolution', '')
        height = _get_resolution_height(resolution)
        
        # 跳过低于360p的视频
        if height and height < 360:
            continue
            
        # 对于每个分辨率只保留最佳格式
        if resolution not in seen_resolutions:
            seen_resolutions.add(resolution)
            filtered_formats.append(f)
    
    # 构建最终的格式列表
    return [{
        'ext': f.get('ext'),
        'resolution': _get_resolution_name(f.get('resolution')),
        'filesize': f.get('filesize'),
        'protocol': f.get('protocol'),
        'vcodec': f.get('vcodec', 'none'),
        'acodec': f.get('acodec', 'none'),
        'url': f.get('url')
    } for f in filtered_formats]

def _get_resolution_height(resolution):
    """从分辨率字符串中提取高度值"""
    try:
        if 'x' in resolution:
            return int(resolution.split('x')[1])
        elif 'p' in resolution:
            return int(resolution.replace('p', ''))
        return None
    except:
        return None

def build_response_data(data, formats):
    """构建响应数据"""
    return {
        'id': data.get('id'),
        'title': data.get('title'),
        'thumbnail': data.get('thumbnail'),
        'view_count': data.get('view_count'),
        'channel': data.get('channel'),
        'channel_id': data.get('channel_id'),
        'upload_date': data.get('upload_date'),
        'formats': formats
    }

def async_save_cache(filename, data, logger, start_time):
    """异步保存缓存文件"""
    try:
        with open(filename, 'w') as f:
            json.dump(data, f)
        logger.info(f"[{time.time() - start_time:.2f}s] Cache saved successfully: {filename}")
    except Exception as e:
        logger.error(f"[{time.time() - start_time:.2f}s] Failed to save cache: {str(e)}")



if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8888)