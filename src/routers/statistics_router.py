from fastapi import APIRouter, HTTPException
from datetime import datetime
from ..models.statistics_models import StatisticsUpdateRequest
from ..models.common_models import ErrorResponse
from ..services.statistics_service import update_channel_statistics
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('statistics_router')

# 创建路由器
router = APIRouter(
    prefix="/api/statistics",
    tags=["数据统计"],
    responses={500: {"model": ErrorResponse}},
)

@router.post(
    "/update",
    summary="手动更新频道统计数据",
    description="手动触发频道统计数据的计算和更新"
)
async def update_statistics(request: StatisticsUpdateRequest):
    """手动触发频道统计数据更新"""
    try:
        target_date = None
        if request.date:
            try:
                target_date = datetime.strptime(request.date, '%Y-%m-%d')
            except ValueError:
                return {"success": False, "error": f"日期格式无效: {request.date}，应为YYYY-MM-DD格式"}
        
        result = update_channel_statistics(request.channel_id, target_date)
        return result
    except Exception as e:
        logger.error(f"手动触发统计数据更新出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"统计数据更新失败: {str(e)}") 