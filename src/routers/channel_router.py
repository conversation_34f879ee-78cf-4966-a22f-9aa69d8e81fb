from fastapi import APIRouter, HTTPException, Query, Depends, BackgroundTasks
from typing import Optional, List
from ..models.channel_models import ChannelResponse, ChannelVideosRequest, ChannelVideosDateRangeRequest
from ..models.common_models import ErrorResponse
from ..utils.youtube_utils import extract_video_id_from_url, extract_channel_id
from ..services.channel_service import get_channel_info, get_video_info
from ..services.task_service import get_channel_videos_by_date_range
from ..dao.task_dao import TaskDao
from ..dao.channel_dao import ChannelDao
from ..utils.logger import get_logger
from datetime import datetime
import asyncio
from pydantic import BaseModel, Field
from src.services.yt_dlp_all_videos import main as yt_dlp_all_videos_main
from src.services.yt_dlp_all_videos import update_video_comment_and_like_count_zero as update_video_comment_and_like_count_zero_main

# 设置日志
logger = get_logger('channel_router')

# 创建路由器
router = APIRouter(
    prefix="/api/channel",
    tags=["频道信息"],
    responses={404: {"model": ErrorResponse}, 400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)

@router.get(
    "/from-video",
    response_model=ChannelResponse,
    summary="根据视频URL或ID获取频道信息",
    description="根据提供的YouTube视频URL或视频ID，返回该视频所属的频道信息"
)
async def get_channel_from_video(
    background_tasks: BackgroundTasks,
    video_url_or_id: str = Query(..., description="YouTube视频URL或ID Example: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
):
    try:
        # 从视频URL或ID中提取视频ID
        video_id = extract_video_id_from_url(video_url_or_id)
        
        # 获取视频信息
        video_info = get_video_info(video_id)
        
        # 从视频信息中获取频道ID
        channel_id = video_info["snippet"]["channelId"]
        
        # 获取频道信息并保存到数据库
        channel_info = get_channel_info(channel_id)
        
        # 异步保存频道下的视频数据

        # 使用FastAPI的BackgroundTasks正确添加后台任务
        youtube_spider = YoutubeSpider(YOUTUBE_API_KEY)
        task_params = {
            "channel_id": channel_id,
            "fetch_type": "channel_videos_recent",
            "max_videos": 50
        }
        task_id = TaskDao.create_task("channel_videos_recent", task_params)
        background_tasks.add_task(process_recent_videos, task_id, channel_id, youtube_spider)


        # 构建响应
        snippet = channel_info["snippet"]
        statistics = channel_info["statistics"]
        
        return {
            "channel_id": channel_id,
            "title": snippet.get("title"),
            "description": snippet.get("description"),
            "custom_url": snippet.get("customUrl"),
            "thumbnail_url": snippet.get("thumbnails", {}).get("high", {}).get("url"),
            "subscriber_count": int(statistics.get("subscriberCount", 0)),
            "video_count": int(statistics.get("videoCount", 0)),
            "view_count": int(statistics.get("viewCount", 0)),
            "published_at": snippet.get("publishedAt"),
            "channel_url": f"https://www.youtube.com/channel/{channel_id}",
            "task_id": task_id,
            "status": "pending"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

@router.get(
    "",
    response_model=ChannelResponse,
    summary="根据频道URL或ID获取频道信息",
    description="根据提供的YouTube频道URL、频道ID或用户名(@username)，返回频道信息"
)
async def get_channel(
    channel_url_or_id: str = Query(..., description="YouTube频道URL、ID或用户名 Example: https://www.youtube.com/@openai")
):
    try:
        # 从频道URL、ID或用户名中提取频道ID
        channel_id = extract_channel_id(channel_url_or_id)
        
        # 获取频道信息并保存到数据库
        channel_info = get_channel_info(channel_id)
        
        # 构建响应
        snippet = channel_info["snippet"]
        statistics = channel_info["statistics"]
        
        return {
            "channel_id": channel_id,
            "title": snippet.get("title"),
            "description": snippet.get("description"),
            "custom_url": snippet.get("customUrl"),
            "thumbnail_url": snippet.get("thumbnails", {}).get("high", {}).get("url"),
            "subscriber_count": int(statistics.get("subscriberCount", 0)),
            "video_count": int(statistics.get("videoCount", 0)),
            "view_count": int(statistics.get("viewCount", 0)),
            "published_at": snippet.get("publishedAt"),
            "channel_url": f"https://www.youtube.com/channel/{channel_id}"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# @router.post(
#     "/videos/date-range",
#     tags=["频道视频"],
#     summary="获取频道指定日期范围内的视频",
#     description="提供YouTube频道URL和日期范围，抓取并保存视频信息到数据库"
# )
# async def get_channel_videos_by_date_range_api(request: ChannelVideosDateRangeRequest):
#     try:
#         # 从频道URL、ID或用户名中提取频道ID
#         channel_id = extract_channel_id(request.channel_url)
        
#         result = get_channel_videos_by_date_range(
#             channel_id, 
#             request.start_date, 
#             request.end_date, 
#             request.max_videos
#         )
        
#         return result
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"处理请求时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

@router.post(
    "/videos/submit_channel_videos_task",
    tags=["频道视频"],
    summary="提交获取频道所有视频的任务",
    description="提供YouTube频道URL，提交一个后台任务来获取并保存频道下的所有视频信息到数据库 Example: https://www.youtube.com/@openai"
)
async def submit_channel_videos_task(request: ChannelVideosRequest):
    try:
        # 从频道URL、ID或用户名中提取频道ID
        channel_id = extract_channel_id(request.channel_url)
        if channel_id is None:
            raise ValueError("无法从提供的URL或ID中提取频道ID")

        # 检查是否已有相同 channel_id 且状态为 pending/running 的任务
        running_task = TaskDao.get_running_task()
        if running_task and running_task.get('channel_id') == channel_id:
            return {
                "message": f"该频道 {channel_id} 的视频获取任务正在处理中",
                "task_id": running_task['id'],
                "status": "running",
                "channel_id": channel_id
            }

        # 正确构建和保存 channel_url
        channel_url = f"https://www.youtube.com/channel/{channel_id}"
        
        # 创建 pending 任务，让调度器负责启动
        task_params = {
            "channel_id": channel_id,
            "fetch_type": "channel_videos",
            "channel_url": channel_url,
            "max_videos": request.max_videos
        }
        
        task_id = TaskDao.create_task("channel_videos", task_params)
        logger.info(f"为频道 {channel_id} 创建了新的待处理任务 {task_id}")

        # 返回成功提交的信息
        return {
            "message": "已成功提交频道历史视频获取任务，任务已加入队列等待处理",
            "channel_id": channel_id,
            "task_id": task_id,
            "status": "pending"
        }

    except ValueError as e:
        logger.error(f"提取频道ID失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        error_msg = f"提交频道视频获取任务时发生错误: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {error_msg}") 

@router.post(
    "/videos/history-recent",
    tags=["频道视频"],
    summary="提交获取频道最近视频的任务",
    description="提供YouTube频道URL，直接获取最近50条视频数据"
)
async def submit_channel_videos_recent_task(request: ChannelVideosRequest, background_tasks: BackgroundTasks):
    try:
        # 从频道URL、ID或用户名中提取频道ID
        channel_id = extract_channel_id(request.channel_url)
        if channel_id is None:
            raise ValueError("无法从提供的URL或ID中提取频道ID")

        channel_info = get_channel_info(channel_id)
        if channel_info is None:
            raise ValueError("无法从提供的URL或ID中提取频道ID")

        # TODO -fwh-直接获取视频数据
        # from ..spider.spider_youtube import process_channel
        youtube_spider = YoutubeSpider(YOUTUBE_API_KEY)
        # process_channel(youtube_spider, channel_id)

        # 创建异步任务，而不是同步处理
        task_params = {
            "channel_id": channel_id,
            "fetch_type": "channel_videos_recent",
            "max_videos": 50
        }
        
        task_id = TaskDao.create_task("channel_videos_recent", task_params)
        logger.info(f"为频道 {channel_id} 创建了新的视频获取任务 {task_id}")
        
        # 使用FastAPI的BackgroundTasks正确添加后台任务
        background_tasks.add_task(process_recent_videos, task_id, channel_id, youtube_spider)
        
        # 立即返回响应
        return {
            "message": "视频获取任务已提交，正在后台处理",
            "task_id": task_id,
            "channel_id": channel_id,
            "status": "pending"
        }

    except ValueError as e:
        logger.error(f"提取频道ID失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        error_msg = f"提交频道视频获取任务时发生错误: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {error_msg}") 
    





import requests
import os
from dotenv import load_dotenv
load_dotenv()

# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")
PROXY_HOST = os.getenv("PROXY_HOST", "127.0.0.1")
PROXY_PORT = os.getenv("PROXY_PORT", "7890")
USE_PROXY = os.getenv("USE_PROXY", "false").lower() == "true"
class YoutubeSpider():
    def __init__(self, api_key):
        self.base_url = "https://www.googleapis.com/youtube/v3/"
        self.api_key = api_key
        self.session = requests.Session()
        
        # 配置代理
        if USE_PROXY:
            self.proxies = {
                'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
                'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
            }
            logger.info(f"使用代理: {self.proxies['http']}")
        else:
            self.proxies = None
            logger.info("不使用代理")
    def get_channel_uploads_id(self, channel_id, part='contentDetails'):
        """取得頻道上傳影片清單的ID"""
        # UC7ia-A8gma8qcdC6GDcjwsQ
        path = f'channels?part={part}&id={channel_id}'
        data = self.get_html_to_json(path)
        if data is None:
            return channel_id
        try:
            uploads_id = data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
        except KeyError:
            uploads_id = None
        return uploads_id
    def get_html_to_json(self, path):
        """发送API请求并处理响应"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                api_url = f"{self.base_url}{path}&key={self.api_key}"
                
                # 发送请求，如果配置了代理则使用代理
                response = self.session.get(
                    api_url,
                    proxies=self.proxies,
                    timeout=10  # 设置超时时间
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 403:
                    logger.error(f"API密钥无效或超出配额: {response.text}")
                    return None
                else:
                    logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                    return None        
            except Exception as e:
                logger.error(f"未知错误: {str(e)}")
                return None
    
    def get_playlist(self, playlist_id, part='contentDetails', max_results=10):
        """取得影片清單ID中的影片"""
        # UU7ia-A8gma8qcdC6GDcjwsQ
        path = f'playlistItems?part={part}&playlistId={playlist_id}&maxResults={max_results}'
        data = self.get_html_to_json(path)
        if not data:
            return []

        video_ids = []
        for data_item in data['items']:
            video_ids.append(data_item['contentDetails']['videoId'])
        return video_ids
    
    def get_video(self, video_id, part='snippet,statistics'):
        """取得影片資訊"""
        # jyordOSr4cI
        # part = 'contentDetails,id,liveStreamingDetails,localizations,player,recordingDetails,snippet,statistics,status,topicDetails'
        path = f'videos?part={part}&id={video_id}'
        data = self.get_html_to_json(path)
        if not data:
            return {}
        
        # 返回完整的API响应数据
        if 'items' in data and len(data['items']) > 0:
            data_item = data['items'][0]
            
            try:
                # 2019-09-29T04:17:05Z
                time_ = datetime.strptime(data_item['snippet']['publishedAt'], '%Y-%m-%dT%H:%M:%SZ')
                formatted_time = time_.strftime('%Y-%m-%d %H:%M:%S')
            except ValueError:
                # 日期格式錯誤
                time_ = None
                formatted_time = None
            
            url_ = f"https://www.youtube.com/watch?v={data_item['id']}"
            
            # 提取缩略图信息
            thumbnails = data_item['snippet'].get('thumbnails', {})
            
            # 优先使用maxres质量的缩略图，如果不存在则依次降级尝试其他质量
            thumbnail_url = None
            for quality in ['maxres', 'standard', 'high', 'medium', 'default']:
                if quality in thumbnails and 'url' in thumbnails[quality]:
                    thumbnail_url = thumbnails[quality]['url']
                    # 找到后立即跳出循环
                    if quality == 'maxres':
                        break
            
            # 提取标签信息
            tags = data_item['snippet'].get('tags', [])
            
            # 构建视频信息，包含所有原始字段
            info = {
                # 原始字段
                'kind': data.get('kind'),
                'etag': data.get('etag'),
                'pageInfo': data.get('pageInfo'),
                
                # 提取字段（保持原有逻辑）
                'id': data_item['id'],
                'channelId': data_item['snippet']['channelId'],  # 使用YouTube channel ID
                'channelTitle': data_item['snippet']['channelTitle'],
                'publishedAt': formatted_time,
                'video_url': url_,
                'title': data_item['snippet']['title'],
                'description': data_item['snippet']['description'],
                'likeCount': data_item['statistics'].get('likeCount', 0),
                'commentCount': data_item['statistics'].get('commentCount', 0),
                'viewCount': data_item['statistics'].get('viewCount', 0),
                
                # 添加标签和缩略图信息
                'tags': tags,
                'thumbnails': thumbnails,
                # 使用优先选择的缩略图URL
                'thumbnailUrl': thumbnail_url,
                
                # 保存原始响应
                'raw_response': data
            }
            return info
        return {}

# 添加异步处理函数
async def process_recent_videos(task_id, channel_id, youtube_spider):
    """异步处理频道最近视频"""
    try:
        # 更新任务状态为运行中
        TaskDao.update_task_status(task_id, "running")
        
        from src.dao.youtube_dao import YouTubeDao
        # 获取上传播放列表ID
        uploads_id = youtube_spider.get_channel_uploads_id(channel_id)
        
        try:
            # 获取播放列表
            video_ids = youtube_spider.get_playlist(uploads_id, max_results=50)
            if not video_ids:
                logger.warning(f"获取播放列表失败或播放列表为空: {uploads_id}")
                TaskDao.update_task_status(task_id, "failed", error_message="获取播放列表失败或为空")
                return
        except Exception as e:
            logger.error(f"获取播放列表失败: {str(e)}")
            TaskDao.update_task_status(task_id, "failed", error_message=f"获取播放列表失败: {str(e)}")
            return
            
        processed_videos = []
        failed_videos = []
        
        # 依次处理每个视频，避免过多并发请求导致API配额超限
        for video_id in video_ids:
            try:
                # 处理单个视频
                result = await process_single_video(video_id, youtube_spider, YouTubeDao)
                if result:
                    processed_videos.append(result)
            except Exception as e:
                logger.error(f"处理视频失败 {video_id}: {str(e)}")
                failed_videos.append(video_id)
        
        # 更新任务状态为成功
        TaskDao.update_task_status(task_id, "success", result={
            "processed_videos": len(processed_videos),
            "failed_videos": len(failed_videos),
            "total": len(video_ids)
        })
        
        logger.info(f"频道 {channel_id} 的视频处理任务已完成，共处理 {len(processed_videos)} 个视频")
        
    except Exception as e:
        logger.error(f"视频处理任务失败: {str(e)}")
        # 更新任务状态为失败
        TaskDao.update_task_status(task_id, "failed", error_message=str(e))

async def process_single_video(video_id, youtube_spider, YouTubeDao):
    """处理单个视频"""
    try:
        # 获取视频详细信息（同步操作）
        video_info = youtube_spider.get_video(video_id)
        
        # 如果获取失败则跳过
        if not video_info:
            logger.warning(f"视频信息获取失败或返回为空: {video_id}")
            return None
            
        # 检查视频是否已存在于数据库
        existing_video = YouTubeDao.get_video_by_id(video_id)
        is_update = existing_video is not None
        
        # 保存到数据库（同步操作）
        YouTubeDao.save_video(video_info)
        
        if is_update:
            logger.info(f"更新视频: {video_info['title']}")
        else:
            logger.info(f"保存新视频: {video_info['title']}")
            
        # 适当延时，避免API请求过快触发限制
        await asyncio.sleep(0.5)
            
        return video_id
    except Exception as e:
        logger.error(f"处理视频失败 {video_id}: {str(e)}")
        # 重新抛出异常
        raise

class BatchChannelImportRequest(BaseModel):
    channel_urls_or_ids: List[str] = Field(..., description="包含YouTube频道URL、ID或用户名的列表", min_items=1)

class BatchChannelImportResponse(BaseModel):
    success_count: int
    failed_count: int
    details: List[dict] # 包含每个频道的处理结果和原因

@router.post(
    "/batch-import",
    response_model=BatchChannelImportResponse,
    tags=["频道信息"],
    summary="批量导入频道信息",
    description="接收一个包含多个YouTube频道URL、ID或用户名的列表，尝试获取并保存每个频道的信息。"
)
async def batch_import_channels(request: BatchChannelImportRequest):
    success_count = 0
    failed_count = 0
    details = []

    for channel_input in request.channel_urls_or_ids:
        channel_id = None
        error_message = None
        status = "failed" # 默认为失败

        try:
            # 提取频道ID
            channel_id = extract_channel_id(channel_input)
            if not channel_id:
                raise ValueError("无法提取频道ID")

            channel_info = ChannelDao.get_channel_by_id(channel_id)
            if channel_info:
                status = "success"
                success_count += 1
                details.append({
                    "input": channel_input,
                    "channel_id": channel_id,
                    "status": status,
                    "error": None
                })
                continue
            
            channel_info = get_channel_info(channel_id)
            if not channel_info:
                 raise ValueError("无法获取频道信息")

            # 如果成功获取，状态设为成功
            status = "success"
            success_count += 1

        except ValueError as e:
            error_message = str(e)
            logger.warning(f"处理频道 '{channel_input}' 失败: {error_message}")
        except HTTPException as e: # 捕获 get_channel_info 可能抛出的HTTPException
            error_message = e.detail
            logger.warning(f"处理频道 '{channel_input}' (ID: {channel_id}) 失败: {error_message}")
        except Exception as e:
            error_message = f"未知错误: {str(e)}"
            logger.error(f"处理频道 '{channel_input}' (ID: {channel_id}) 时发生意外错误", exc_info=True)

        if status == "failed":
            failed_count += 1

        details.append({
            "input": channel_input,
            "channel_id": channel_id,
            "status": status,
            "error": error_message
        })

        # 增加短暂延时，避免短时间大量请求YouTube API
        await asyncio.sleep(0.2)

    return BatchChannelImportResponse(
        success_count=success_count,
        failed_count=failed_count,
        details=details
    )



@router.put("/video/update_video_by_yt_dlp" ,tags=["频道视频"], summary="更新视频信息", description="更新视频发布时间为空的视频数据")
async def update_video_by_yt_dlp():
    try:
        yt_dlp_all_videos_main()
        return {"message": "视频信息更新成功"}
    except Exception as e:
        logger.error(f"更新视频信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新视频信息失败: {str(e)}")


@router.put("/video/update_video_comment_and_like_count_zero" ,tags=["频道视频"], summary="更新视频信息", description="更新视频阅读量和点赞量都是0的视频数据")
async def update_video_comment_and_like_count_zero():
    try:
        update_video_comment_and_like_count_zero_main()
        return {"message": "视频信息更新成功"}
    except Exception as e:
        logger.error(f"更新视频信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新视频信息失败: {str(e)}")
