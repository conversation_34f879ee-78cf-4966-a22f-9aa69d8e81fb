from fastapi import APIRouter, HTTPException, Path
from ..models.common_models import ErrorResponse
from ..dao.task_dao import TaskDao
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('task_router')

# 创建路由器
router = APIRouter(
    prefix="/api/tasks",
    tags=["任务管理"],
    responses={404: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)

@router.get(
    "/{task_id}",
    summary="查询任务状态",
    description="根据任务ID查询任务的当前状态、进度和结果"
)
async def get_task_status(task_id: str = Path(..., description="任务ID")):
    try:
        # 获取任务信息
        task_info = TaskDao.get_task_by_id(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail=f"未找到任务: {task_id}")
        
        # 构建响应
        response = {
            "task_id": task_id,
            "status": task_info.get("status", "unknown"),
            "task_type": task_info.get("task_type", ""),
            "created_at": task_info.get("created_at", ""),
            "updated_at": task_info.get("updated_at", ""),
        }
        
        # 添加结果或错误信息（如果有）
        if task_info.get("result"):
            response["result"] = task_info["result"]
        if task_info.get("error_message"):
            response["error"] = task_info["error_message"]
            
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}") 