from fastapi import APIRouter, HTTPException
from ..models.video_models import VideoDownloadRequest, VideoClipRequest, AudioDownloadRequest
from ..models.common_models import ErrorResponse
from ..services.video_service import download_video, download_video_clip, download_audio
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('video_router')

# 创建路由器
router = APIRouter(
    prefix="/api",
    tags=["视频下载"],
    responses={500: {"model": ErrorResponse}},
)

@router.post(
    "/video/download",
    summary="下载YouTube视频",
    description="提供YouTube视频URL，下载完整视频"
)
async def download_video_api(request: VideoDownloadRequest):
    try:
        result = download_video(request.video_url, request.format)
        return result
    except Exception as e:
        logger.error(f"下载视频失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载视频失败: {str(e)}")

@router.post(
    "/video/download/clip",
    summary="下载YouTube视频片段",
    description="提供YouTube视频URL和时间范围，下载视频片段"
)
async def download_video_clip_api(request: VideoClipRequest):
    try:
        result = download_video_clip(
            request.video_url, 
            request.start_time, 
            request.end_time, 
            request.format
        )
        return result
    except Exception as e:
        logger.error(f"下载视频片段失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载视频片段失败: {str(e)}")

@router.post(
    "/audio/download",
    summary="下载YouTube视频的音频",
    description="提供YouTube视频URL，仅下载音频部分"
)
async def download_audio_api(request: AudioDownloadRequest):
    try:
        result = download_audio(request.video_url, request.format)
        return result
    except Exception as e:
        logger.error(f"下载音频失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载音频失败: {str(e)}") 
    
