"""Routers package for the application."""
from fastapi import APIRouter
from .channel_router import router as channel_router
from .video_router import router as video_router
from .task_router import router as task_router
from .statistics_router import router as statistics_router

# 创建主路由器
router = APIRouter()

# 包含所有路由器
router.include_router(channel_router)
router.include_router(video_router)
router.include_router(task_router)
router.include_router(statistics_router) 