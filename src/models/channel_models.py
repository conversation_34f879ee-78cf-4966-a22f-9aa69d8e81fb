from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, Dict, Any, List

class ChannelResponse(BaseModel):
    channel_id: str = Field(..., description="YouTube频道ID")
    title: Optional[str] = Field(None, description="频道标题")
    description: Optional[str] = Field(None, description="频道描述")
    custom_url: Optional[str] = Field(None, description="自定义URL")
    thumbnail_url: Optional[str] = Field(None, description="频道缩略图URL")
    subscriber_count: Optional[int] = Field(None, description="订阅者数量")
    video_count: Optional[int] = Field(None, description="视频数量")
    view_count: Optional[int] = Field(None, description="总观看次数")
    published_at: Optional[str] = Field(None, description="频道创建时间")
    channel_url: str = Field(..., description="频道URL")

class ChannelVideosRequest(BaseModel):
    channel_url: str = Field(..., description="YouTube频道URL、ID或用户名(@username)")
    max_videos: int = Field(50, description="最大获取视频数量，默认50")

class ChannelVideosDateRangeRequest(BaseModel):
    channel_url: str = Field(..., description="YouTube频道URL、ID或用户名(@username)")
    start_date: str = Field(..., description="开始日期(YYYYMMDD格式，如20240101)")
    end_date: str = Field(..., description="结束日期(YYYYMMDD格式，如20240131)")
    max_videos: int = Field(50, description="最大获取视频数量，默认50") 