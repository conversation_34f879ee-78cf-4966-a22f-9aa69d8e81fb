from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List

class VideoDownloadRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    format: str = Field("best", description="视频格式，例如：best, bestvideo+bestaudio, 360p, 720p")

class VideoClipRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    start_time: float = Field(..., description="开始时间（秒）")
    end_time: float = Field(..., description="结束时间（秒）")
    format: str = Field("best[ext=mp4]", description="视频格式")

class AudioDownloadRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    format: str = Field("bestaudio[ext=m4a]", description="音频格式") 