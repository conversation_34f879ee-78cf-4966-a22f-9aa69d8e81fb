import json
import uuid
import psycopg2.extras
from ..database.db_pool import DatabasePool
from ..utils.logger import get_logger

# 注册UUID类型
psycopg2.extras.register_uuid()

logger = get_logger('raw_fetch_dao')

class RawFetchDao:
    """数据访问对象，处理与 ytb_raw_fetch 表相关的数据库操作"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建原始数据抓取表（如果不存在）"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 使用PostgreSQL语法创建表
            sql = """
                CREATE TABLE IF NOT EXISTS ytb_raw_fetch (
                    id BIGSERIAL PRIMARY KEY,
                    task_id UUID NOT NULL,
                    endpoint VARCHAR(255) NOT NULL,
                    response JSONB NOT NULL,
                    params JSONB,
                    etag VARCHAR(255),
                    quota_cost INTEGER NOT NULL DEFAULT 1,
                    fetched_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_raw_fetch_task_id ON ytb_raw_fetch(task_id);
                CREATE INDEX IF NOT EXISTS idx_ytb_raw_fetch_endpoint ON ytb_raw_fetch(endpoint);
            """
            
            cursor.execute(sql)
            conn.commit()
            logger.info("ytb_raw_fetch表创建成功或已存在")
            return True
            
        except Exception as e:
            logger.error(f"创建ytb_raw_fetch表失败: {str(e)}")
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def save_raw_data(task_id, endpoint, response_data, params=None, etag=None, quota_cost=1):
        """保存原始API响应数据
        
        Args:
            task_id (str): 关联的任务ID
            endpoint (str): API端点
            response_data (dict): API响应数据
            params (dict, optional): 请求参数
            etag (str, optional): 资源ETag
            quota_cost (int, optional): API配额消耗量
            
        Returns:
            int: 插入记录的ID，失败时返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                INSERT INTO ytb_raw_fetch (
                    task_id, endpoint, response, params, etag, quota_cost,
                    fetched_at, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s,
                    NOW(), NOW(), NOW()
                ) RETURNING id
            """
            
            # 确保task_id是UUID类型
            if isinstance(task_id, str):
                task_id = uuid.UUID(task_id)
                
            # 确保response_data和params是JSON格式
            if not isinstance(response_data, str):
                response_data = json.dumps(response_data)
                
            if params is not None and not isinstance(params, str):
                params = json.dumps(params)
            
            cursor.execute(sql, (
                task_id,
                endpoint,
                response_data,
                params,
                etag,
                quota_cost
            ))
            
            result = cursor.fetchone()
            conn.commit()
            
            logger.debug(f"成功保存原始数据: endpoint={endpoint}, task_id={task_id}")
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"保存原始数据失败: {str(e)}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_raw_data_by_task(task_id):
        """获取任务相关的原始数据
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            list: 原始数据列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT * FROM ytb_raw_fetch 
                WHERE task_id = %s 
                AND deleted_at IS NULL
                ORDER BY fetched_at DESC
            """
            
            # 确保task_id是UUID类型
            if isinstance(task_id, str):
                task_id = uuid.UUID(task_id)
                
            cursor.execute(sql, (task_id,))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"获取原始数据失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_raw_data_by_endpoint(endpoint, limit=10):
        """获取特定端点的原始数据
        
        Args:
            endpoint (str): API端点
            limit (int): 最大返回数量
            
        Returns:
            list: 原始数据列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT * FROM ytb_raw_fetch 
                WHERE endpoint = %s 
                AND deleted_at IS NULL
                ORDER BY fetched_at DESC
                LIMIT %s
            """
            
            cursor.execute(sql, (endpoint, limit))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"获取原始数据失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 