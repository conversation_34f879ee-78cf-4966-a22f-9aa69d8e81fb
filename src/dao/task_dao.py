import json
import uuid
from datetime import datetime, timedelta
from ..database.db_pool import DatabasePool
from ..utils.logger import get_logger
from typing import List, Dict, Optional
from pytz import timezone

logger = get_logger('task_dao')

class TaskDao:
    """数据访问对象，处理与 ytb_tasks 表相关的数据库操作"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建任务表（如果不存在）"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 使用PostgreSQL语法创建表
            sql = """
                CREATE TABLE IF NOT EXISTS ytb_tasks (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    task_type VARCHAR(50) NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'pending',
                    parameters JSONB NOT NULL,
                    result JSONB,
                    error_message TEXT,
                    started_at TIMESTAMPTZ,
                    completed_at TIMESTAMPTZ,
                    retry_count INTEGER NOT NULL DEFAULT 0,
                    next_retry_at TIMESTAMPTZ,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_tasks_status ON ytb_tasks(status);
                CREATE INDEX IF NOT EXISTS idx_ytb_tasks_created_at ON ytb_tasks(created_at);
                CREATE INDEX IF NOT EXISTS idx_ytb_tasks_next_retry_at ON ytb_tasks(next_retry_at);
            """
            
            cursor.execute(sql)
            conn.commit()
            logger.info("ytb_tasks表创建成功或已存在")
            return True
            
        except Exception as e:
            logger.error(f"创建ytb_tasks表失败: {str(e)}")
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def create_task(task_type: str, parameters: Dict) -> str:
        """创建新任务"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 使用当前时间（带时区）
            now = datetime.now().astimezone(timezone('Asia/Shanghai'))
            
            sql = """
                INSERT INTO ytb_tasks (
                    id, task_type, status, parameters, created_at, updated_at
                ) VALUES (
                    %s, %s, 'pending', %s, %s, %s
                ) RETURNING id
            """
            
            cursor.execute(sql, (
                task_id,
                task_type,
                json.dumps(parameters),
                now,
                now
            ))
            
            conn.commit()
            return task_id
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def update_task_status(task_id: str, status: str, result: Optional[Dict] = None, error_message: Optional[str] = None) -> bool:
        """更新任务状态"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 使用当前时间（带时区）
            now = datetime.now().astimezone(timezone('Asia/Shanghai'))
            
            sql = """
                UPDATE ytb_tasks 
                SET status = %s,
                    result = %s,
                    error_message = %s,
                    updated_at = %s
                WHERE id = %s
            """
            
            cursor.execute(sql, (
                status,
                json.dumps(result) if result else None,
                error_message,
                now,
                task_id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_pending_tasks(limit=10):
        """获取待处理的任务
        
        Args:
            limit (int): 最大返回数量
            
        Returns:
            list: 任务列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT * FROM ytb_tasks 
                WHERE status = 'pending' 
                AND (next_retry_at IS NULL OR next_retry_at <= NOW())
                AND deleted_at IS NULL
                ORDER BY created_at ASC
                LIMIT %s
            """
            
            cursor.execute(sql, (limit,))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"获取待处理任务失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
                
    @staticmethod
    def set_task_retry(task_id, next_retry_time, error_message=None):
        """设置任务重试
        
        Args:
            task_id (str): 任务ID
            next_retry_time (datetime): 下次重试时间
            error_message (str, optional): 错误信息
            
        Returns:
            bool: 操作是否成功
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                UPDATE ytb_tasks SET 
                retry_count = retry_count + 1,
                next_retry_at = %s,
                error_message = CASE WHEN %s IS NOT NULL THEN %s ELSE error_message END,
                updated_at = NOW()
                WHERE id = %s
            """
            
            cursor.execute(sql, (next_retry_time, error_message, error_message, task_id))
            
            conn.commit()
            logger.info(f"设置任务重试成功: {task_id}, 下次重试时间: {next_retry_time}")
            return True
            
        except Exception as e:
            logger.error(f"设置任务重试失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_recent_tasks(channel_id: str, task_type: str, time_window: timedelta) -> List[Dict]:
        """
        获取指定频道在时间窗口内的任务
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 计算时间窗口的起始时间
            start_time = datetime.now() - time_window
            
            # 查询最近的任务
            sql = """
                SELECT id, task_type, status, created_at, updated_at, 
                       parameters, result, error_message,
                       started_at, completed_at, retry_count, next_retry_at
                FROM ytb_tasks
                WHERE parameters->>'channel_id' = %s
                AND task_type = %s
                AND created_at >= %s
                AND deleted_at IS NULL
                ORDER BY created_at DESC
            """
            
            cursor.execute(sql, (channel_id, task_type, start_time))
            tasks = cursor.fetchall()
            
            logger.info(f"获取到{len(tasks)}个最近任务")
            return tasks
            
        except Exception as e:
            logger.error(f"获取最近任务失败: {str(e)}")
            return []
            
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 
    
    @staticmethod
    def get_running_task() -> Optional[Dict]:
        """
        获取最近1小时内正在运行的任务信息
        返回包含任务ID和channel_id的字典，如果没有则返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 查询最近1小时内正在运行的任务
            sql = """
                SELECT id, parameters->>'channel_id' as channel_id
                FROM ytb_tasks
                WHERE status = 'running'
                AND created_at >= NOW() - INTERVAL '1 hour'
                AND deleted_at IS NULL
                ORDER BY created_at DESC
                LIMIT 1
            """
            
            cursor.execute(sql)
            result = cursor.fetchone()
            
            if result:
                logger.info(f"找到正在运行的任务: {result['id']}")
                return result
            
            return None
            
        except Exception as e:
            logger.error(f"获取运行中任务失败: {str(e)}")
            return None
            
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 

    @staticmethod
    def get_next_pending_task() -> Optional[Dict]:
        """获取下一个待处理的任务（按创建时间排序）"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            # 使用字典游标，方便返回字典格式
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)

            sql = """
                SELECT id, task_type, status, parameters, created_at, updated_at, 
                parameters->>'channel_id' as channel_id
                FROM ytb_tasks
                WHERE status = 'pending'
                AND deleted_at IS NULL
                AND (next_retry_at IS NULL OR next_retry_at <= NOW()) -- 尊重重试逻辑
                ORDER BY created_at ASC
                LIMIT 1
            """

            cursor.execute(sql)
            # fetchone() 返回单个记录或 None
            result = cursor.fetchone()
            return result # 返回字典或 None

        except Exception as e:
            logger.error(f"获取下一个待处理任务失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 

    @staticmethod
    def get_task_by_id(task_id: str) -> Optional[Dict]:
        """根据任务ID获取任务详细信息"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT id, task_type, status, parameters, result, error_message,
                       started_at, completed_at, retry_count, created_at, updated_at,
                       parameters->>'channel_id' as channel_id
                FROM ytb_tasks
                WHERE id = %s AND deleted_at IS NULL
            """
            
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()
            
            # 如果结果包含JSONB字段，它们将被转换为Python对象
            
            return result
            
        except Exception as e:
            logger.error(f"根据ID获取任务失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 