import json
import logging
from datetime import datetime
from ..database.db_pool import DatabasePool
from ..utils.logger import get_logger
from sqlalchemy import desc

logger = get_logger('youtube_dao')

class YouTubeDao:
    """数据访问对象，处理与 ytb_videos 表相关的数据库操作"""
    
    @staticmethod
    def create_table_if_not_exists():
        """创建YouTube视频表和视频统计快照表（如果不存在）"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 使用PostgreSQL语法创建视频表
            sql_videos = """
                CREATE TABLE IF NOT EXISTS ytb_videos (
                    id BIGSERIAL PRIMARY KEY,
                    youtube_id VARCHAR(50) NOT NULL UNIQUE,
                    channel_id VARCHAR(50) NOT NULL,  -- 修改为使用YouTube的channel_id
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    published_at TIMESTAMPTZ,
                    duration INTERVAL,
                    dimension VARCHAR(10),
                    caption BOOLEAN DEFAULT FALSE,
                    licensed_content BOOLEAN DEFAULT FALSE,
                    tags TEXT[],                       -- 存储视频标签
                    thumbnails JSONB,                  -- 存储完整的缩略图信息
                    category_id VARCHAR(50),           -- 分类ID
                    thumbnail_url TEXT,                -- 为兼容性保留
                    view_count BIGINT,
                    like_count INTEGER,
                    dislike_count INTEGER,
                    favorite_count INTEGER,
                    comment_count INTEGER,
                    live_broadcast_content VARCHAR(20),
                    privacy_status VARCHAR(20),
                    etag VARCHAR(255),         -- YouTube API响应的etag字段
                    page_info JSONB,           -- YouTube API响应的pageInfo字段
                    raw_data JSONB,            -- 存储完整的原始API响应
                    last_refreshed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_videos_youtube_id ON ytb_videos(youtube_id);
                CREATE INDEX IF NOT EXISTS idx_ytb_videos_channel_id ON ytb_videos(channel_id);
                CREATE INDEX IF NOT EXISTS idx_ytb_videos_published_at ON ytb_videos(published_at);
                CREATE INDEX IF NOT EXISTS idx_ytb_videos_last_refreshed_at ON ytb_videos(last_refreshed_at);
            """
            
            # 创建视频统计快照表
            sql_snapshots = """
                CREATE TABLE IF NOT EXISTS ytb_video_stats_snapshots (
                    id BIGSERIAL PRIMARY KEY,
                    video_youtube_id VARCHAR(50) NOT NULL,  -- 使用YouTube视频ID而不是数据库ID
                    view_count BIGINT NOT NULL DEFAULT 0,
                    like_count INTEGER NOT NULL DEFAULT 0,
                    dislike_count INTEGER NOT NULL DEFAULT 0,
                    favorite_count INTEGER NOT NULL DEFAULT 0,
                    comment_count INTEGER NOT NULL DEFAULT 0,
                    snapshot_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_video_stats_video_youtube_id_snapshot_at 
                ON ytb_video_stats_snapshots(video_youtube_id, snapshot_at);
                
                -- 添加外键约束
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_constraint WHERE conname = 'fk_ytb_video_stats_video_youtube_id'
                    ) THEN
                        ALTER TABLE ytb_video_stats_snapshots
                        ADD CONSTRAINT fk_ytb_video_stats_video_youtube_id
                        FOREIGN KEY (video_youtube_id)
                        REFERENCES ytb_videos(youtube_id)
                        ON DELETE CASCADE;
                    END IF;
                END
                $$;
            """
            
            cursor.execute(sql_videos)
            cursor.execute(sql_snapshots)
            
            conn.commit()
            logger.debug("YouTube视频表和视频统计快照表创建成功或已存在")
            return True
            
        except Exception as e:
            logger.error(f"创建YouTube视频相关表失败: {str(e)}")
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def save_video(video_data):
        """保存YouTube视频数据到数据库
        
        Args:
            video_data (dict): 视频数据字典
            
        Returns:
            dict: 包含操作结果的字典，键包括：
                - success (bool): 操作是否成功
                - operation (str): 'insert'或'update'
                - message (str): 操作消息
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 直接使用YouTube频道ID
            channel_id = video_data.get('channelId')
            youtube_id = video_data.get('id')
            
            if not channel_id:
                logger.error(f"保存视频失败，缺少channelId: {youtube_id}")
                return {'success': False, 'message': f"缺少channelId: {youtube_id}"}
            
            # 处理发布时间
            published_at = video_data.get('publishedAt')
            if published_at and isinstance(published_at, str):
                # 如果是ISO格式，转换为PostgreSQL格式
                if 'T' in published_at:
                    published_at = published_at.replace('T', ' ').replace('Z', '')
            
            # 处理持续时间
            duration_str = video_data.get('duration', 'PT0S')
            # TODO: 将ISO 8601 持续时间格式转换为PostgreSQL interval
            
            # 处理标签
            tags = video_data.get('tags', [])
            if isinstance(tags, str):
                tags = [tag.strip() for tag in tags.split(',')]
            
            # 处理缩略图
            thumbnail_url = video_data.get('thumbnailUrl')
            
            # 检查视频是否已存在
            check_sql = "SELECT 1 FROM ytb_videos WHERE youtube_id = %s AND deleted_at IS NULL"
            cursor.execute(check_sql, (youtube_id,))
            video_exists = cursor.fetchone() is not None
            
            if video_exists:
                # 如果视频已存在，执行更新操作
                update_sql = """
                    UPDATE ytb_videos SET
                        title = %s,
                        description = %s,
                        published_at = %s,
                        duration = %s,
                        thumbnail_url = %s,
                        thumbnails = %s,
                        view_count = %s,
                        like_count = %s,
                        comment_count = %s,
                        favorite_count = %s,
                        tags = %s,
                        category_id = %s,
                        etag = %s,
                        page_info = %s,
                        raw_data = %s,
                        last_refreshed_at = NOW(),
                        updated_at = NOW()
                    WHERE youtube_id = %s
                    RETURNING id
                """
                
                cursor.execute(update_sql, (
                    video_data.get('title'),                     # title
                    video_data.get('description'),               # description
                    published_at,                                # published_at
                    duration_str,                                # duration
                    thumbnail_url,                               # thumbnail_url
                    json.dumps(video_data.get('thumbnails', {})),# thumbnails
                    int(video_data.get('viewCount', 0)),         # view_count
                    int(video_data.get('likeCount', 0)),         # like_count
                    int(video_data.get('commentCount', 0)),      # comment_count
                    int(video_data.get('favoriteCount', 0)),     # favorite_count
                    tags,                                        # tags
                    video_data.get('categoryId'),                # category_id
                    video_data.get('etag'),                      # etag
                    json.dumps(video_data.get('pageInfo', {})),  # page_info
                    json.dumps(video_data.get('raw_response', {})), # raw_data
                    youtube_id                                   # WHERE条件
                ))
                
                operation = 'update'
            else:
                # 如果视频不存在，执行插入操作
                insert_sql = """
                    INSERT INTO ytb_videos (
                        youtube_id, channel_id, title, description, 
                        published_at, duration, thumbnail_url, thumbnails,
                        view_count, like_count, comment_count, favorite_count,
                        tags, category_id, etag, page_info, raw_data, 
                        last_refreshed_at, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, 
                        %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        NOW(), NOW(), NOW()
                    )
                    RETURNING id
                """
                
                cursor.execute(insert_sql, (
                    youtube_id,                                  # youtube_id
                    channel_id,                                  # channel_id (YouTube ID)
                    video_data.get('title'),                     # title
                    video_data.get('description'),               # description
                    published_at,                                # published_at
                    duration_str,                                # duration
                    thumbnail_url,                               # thumbnail_url
                    json.dumps(video_data.get('thumbnails', {})),# thumbnails
                    int(video_data.get('viewCount', 0)),         # view_count
                    int(video_data.get('likeCount', 0)),         # like_count
                    int(video_data.get('commentCount', 0)),      # comment_count
                    int(video_data.get('favoriteCount', 0)),     # favorite_count
                    tags,                                        # tags
                    video_data.get('categoryId'),                # category_id
                    video_data.get('etag'),                      # etag
                    json.dumps(video_data.get('pageInfo', {})),  # page_info
                    json.dumps(video_data.get('raw_response', {})) # raw_data
                ))
                
                operation = 'insert'

            result = cursor.fetchone()
            conn.commit()
            
            # 保存视频统计快照
            YouTubeDao.save_video_stats_snapshot(
                youtube_id,  # 使用YouTube视频ID
                int(video_data.get('viewCount', 0)),
                int(video_data.get('likeCount', 0)),
                int(video_data.get('dislikeCount', 0)),
                int(video_data.get('favoriteCount', 0)),
                int(video_data.get('commentCount', 0))
            )
            
            logger.info(f"{operation}视频: 视频ID {youtube_id} 视频标题 {video_data.get('title')},viewCount={video_data.get('viewCount')}, likeCount={video_data.get('likeCount')}, commentCount={video_data.get('commentCount')}")
            
            return {
                'success': True,
                'operation': operation,
                'message': f"{operation}视频成功: {youtube_id}"
            }
            
        except Exception as e:
            logger.error(f"保存视频失败: {str(e)}")
            if conn:
                conn.rollback()
            return {
                'success': False,
                'message': f"保存视频失败: {str(e)}"
            }
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def save_video_stats_snapshot(video_youtube_id, view_count, like_count, dislike_count, favorite_count, comment_count):
        """保存视频统计数据快照
        
        Args:
            video_youtube_id (str): 视频的YouTube ID
            view_count (int): 观看次数
            like_count (int): 点赞数
            dislike_count (int): 不喜欢数
            favorite_count (int): 收藏数
            comment_count (int): 评论数
            
        Returns:
            bool: 操作是否成功
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                INSERT INTO ytb_video_stats_snapshots (
                    video_youtube_id, view_count, like_count, dislike_count,
                    favorite_count, comment_count,
                    snapshot_at, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, NOW(), NOW(), NOW()
                )
            """
            
            cursor.execute(sql, (
                video_youtube_id,
                view_count,
                like_count,
                dislike_count,
                favorite_count,
                comment_count
            ))
            
            conn.commit()
            logger.debug(f"成功保存视频统计快照: 视频ID={video_youtube_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存视频统计快照失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_video_by_id(video_id):
        """根据视频ID获取视频信息
        
        Args:
            video_id (str): YouTube视频ID
        
        Returns:
            dict: 视频信息，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT * FROM ytb_videos 
                WHERE youtube_id = %s
                AND deleted_at IS NULL
            """
            
            cursor.execute(sql, (video_id,))
            result = cursor.fetchone()
            
            return result
            
        except Exception as e:
            logger.error(f"查询YouTube视频失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_all_videos(limit=100, offset=0):
        """获取所有视频
        
        Args:
            limit (int): 限制返回的记录数
            offset (int): 偏移量，用于分页
        
        Returns:
            list: 视频列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT * FROM ytb_videos 
                WHERE deleted_at IS NULL
                ORDER BY published_at DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (limit, offset))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询YouTube视频列表失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_videos_by_channel_id(channel_id, limit=100, offset=0):
        """根据频道ID获取视频
        
        Args:
            channel_id (str): 频道ID
            limit (int): 限制返回的记录数
            offset (int): 偏移量，用于分页
        
        Returns:
            list: 视频列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 直接使用YouTube频道ID查询
            sql = """
                SELECT id, youtube_id, channel_id, title, description, published_at, duration, thumbnail_url, view_count, like_count, comment_count, favorite_count, tags, category_id, etag, page_info, raw_data, last_refreshed_at, created_at, updated_at, deleted_at FROM ytb_videos 
                WHERE channel_id = %s
                AND deleted_at IS NULL
                ORDER BY published_at DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (channel_id, limit, offset))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询频道视频列表失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_video_by_youtube_id(youtube_id):
        """
        根据YouTube ID获取视频信息
        
        Args:
            youtube_id (str): YouTube视频ID
            
        Returns:
            list: 视频信息列表，每个元素为[youtube_id, view_count, like_count, comment_count]
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 直接使用YouTube频道ID查询
            sql = """
                SELECT youtube_id, view_count, like_count, comment_count FROM ytb_videos 
                WHERE youtube_id = %s
                AND deleted_at IS NULL
                ORDER BY published_at DESC
            """
            
            cursor.execute(sql, (youtube_id,))
            results = cursor.fetchall()
            
            # 确保返回列表格式
            if results:
                return [list(row) for row in results]
            return []
            
        except Exception as e:
            logger.error(f"获取视频信息失败 (ID: {youtube_id}): {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)

    @staticmethod
    def get_video_by_published_at_is_null(channel_id):
        """
        根据发布时间获取视频信息
        
        Args:
            channel_id (str): 频道ID
        
        Returns:
            list: 视频信息列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()  

            sql = """
                SELECT id, youtube_id, channel_id, title, description, published_at FROM ytb_videos 
                WHERE channel_id = %s
                AND published_at IS NULL
                AND deleted_at IS NULL
            """ 
            cursor.execute(sql, (channel_id,))
            results = cursor.fetchall()
            return results
        except Exception as e:
            logger.error(f"查询视频信息失败: {str(e)}")
            return []
        finally:
            if conn and cursor:  # 确保 conn 和 cursor 已被赋值
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod   
    def get_video_by_comment_count_and_like_count_zero(channel_id):
        """
        获取评论量和点赞量都是0的视频
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()

            sql = """
                SELECT ID, youtube_id, title, description, published_at, view_count, like_count, comment_count, favorite_count FROM ytb_videos 
                WHERE comment_count = 0 AND like_count = 0
                AND channel_id = %s
                AND deleted_at IS NULL
            """
            cursor.execute(sql, (channel_id,))
            results = cursor.fetchall()
            return results
        except Exception as e:
            logger.error(f"查询视频信息失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)

    @staticmethod
    def update_video(video_data):
        """
        更新视频信息
        
        Args:
            video_data (dict): 视频数据
            
        Returns:
            bool: 更新是否成功
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 视频ID，根据传入数据参数名可能不同
            video_id = video_data.get('youtube_id') or video_data.get('id')
            if not video_id:
                logger.error("更新视频失败: 缺少视频ID")
                return False
            
            # 更新视频基本信息和统计数据
            sql = """
                UPDATE ytb_videos SET 
                    view_count = %s, 
                    like_count = %s, 
                    comment_count = %s,
                    last_refreshed_at = NOW(),
                    updated_at = NOW()
                WHERE youtube_id = %s
                AND deleted_at IS NULL
            """
            
            cursor.execute(sql, (
                video_data.get('view_count', 0) or video_data.get('viewCount', 0), 
                video_data.get('like_count', 0) or video_data.get('likeCount', 0), 
                video_data.get('comment_count', 0) or video_data.get('commentCount', 0),
                video_id
            ))
            
            # 保存视频统计快照
            YouTubeDao.save_video_stats_snapshot(
                video_id,
                video_data.get('view_count', 0) or video_data.get('viewCount', 0),
                video_data.get('like_count', 0) or video_data.get('likeCount', 0),
                video_data.get('dislike_count', 0) or video_data.get('dislikeCount', 0) or 0,
                video_data.get('favorite_count', 0) or video_data.get('favoriteCount', 0) or 0,
                video_data.get('comment_count', 0) or video_data.get('commentCount', 0)
            )
            
            conn.commit()
            logger.info(f"视频统计数据更新成功: ID={video_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新视频失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @classmethod
    def get_channel_videos(cls, channel_id, year=None, days=365, limit=20, offset=0):
        """
        获取频道在指定年份或天数内的视频列表
        
        Args:
            channel_id (str): 频道YouTube ID
            year (int): 指定年份，优先级高于days
            days (int): 获取多少天内的视频，默认365天
            limit (int): 每页数量限制
            offset (int): 分页偏移量
        
        Returns:
            list: 视频信息列表
        """
        try:
            from datetime import date, timedelta
            from sqlalchemy import and_
            from dao.channel_dao import ChannelDao
            
            # 获取频道的内部ID
            channel = ChannelDao.get_channel_by_youtube_id(channel_id)
            if not channel:
                logger.error(f"找不到频道 {channel_id}")
                return []
                
            channel_id_internal = channel['id']
            
            session = get_session()
            query = session.query(Video).filter(Video.channel_id == channel_id_internal)
            
            if year is not None:
                # 如果指定了年份，按年份过滤
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)
                query = query.filter(
                    and_(
                        Video.published_at >= start_date,
                        Video.published_at <= end_date
                    )
                )
            else:
                # 否则按天数过滤
                cutoff_date = datetime.now() - timedelta(days=days)
                query = query.filter(Video.published_at >= cutoff_date)
            
            # 添加排序、分页
            videos = query.order_by(desc(Video.published_at))\
                .limit(limit).offset(offset)\
                .all()
            
            # 转换为字典列表
            return [video.to_dict() for video in videos]
            
        except Exception as e:
            logger.error(f"获取频道视频列表失败 (频道ID: {channel_id}): {str(e)}")
            return [] 