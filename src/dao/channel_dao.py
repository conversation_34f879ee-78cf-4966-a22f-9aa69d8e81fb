import json
import logging
from datetime import datetime
from ..database.db_pool import DatabasePool
from ..utils.logger import get_logger

logger = get_logger('channel_dao')

class ChannelDao:
    """数据访问对象，处理与 ytb_channels 表相关的数据库操作"""
    
    @staticmethod
    def save_channel(channel_data):
        """保存YouTube频道数据到数据库
        
        Args:
            channel_data (dict): 包含频道信息的字典
        
        Returns:
            bool: 操作是否成功
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                INSERT INTO ytb_channels (
                    channel_id, title, description, custom_url, 
                    published_at, country, view_count, subscriber_count, 
                    video_count, thumbnail_url, banner_url, last_refreshed_at,
                    is_verified, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, 
                    %s, %s, %s, %s,
                    %s, %s, %s, NOW(),
                    %s, NOW(), NOW()
                ) ON CONFLICT (channel_id) DO UPDATE SET
                    title = EXCLUDED.title,
                    description = EXCLUDED.description,
                    custom_url = EXCLUDED.custom_url,
                    country = EXCLUDED.country,
                    view_count = EXCLUDED.view_count,
                    subscriber_count = EXCLUDED.subscriber_count,
                    video_count = EXCLUDED.video_count,
                    thumbnail_url = EXCLUDED.thumbnail_url,
                    banner_url = EXCLUDED.banner_url,
                    last_refreshed_at = NOW(),
                    updated_at = NOW(),
                    is_verified = EXCLUDED.is_verified
                RETURNING id
            """ 
            
            # 处理发布时间
            published_at = channel_data.get('publishedAt')
            if published_at and 'T' in published_at:
                # 将ISO格式转换为PostgreSQL timestamp格式
                published_at = published_at.replace('T', ' ').replace('Z', '')
            
            cursor.execute(sql, (
                channel_data.get('id'),                              # channel_id
                channel_data.get('title'),                           # title
                channel_data.get('description'),                     # description
                channel_data.get('customUrl'),                       # custom_url
                published_at,                                        # published_at
                channel_data.get('country'),                         # country
                int(channel_data.get('viewCount', 0)),               # view_count
                int(channel_data.get('subscriberCount', 0)),         # subscriber_count
                int(channel_data.get('videoCount', 0)),              # video_count
                channel_data.get('thumbnailUrl'),                    # thumbnail_url
                channel_data.get('bannerUrl'),                       # banner_url
                channel_data.get('isVerified', False)                # is_verified
            ))
            
            result = cursor.fetchone()
            conn.commit()
            
            # 记录频道统计快照
            if result:
                youtube_channel_id = channel_data.get('id')  # 使用YouTube的channel_id
                ChannelDao.save_channel_stats_snapshot(
                    youtube_channel_id,  # 使用YouTube的channel_id
                    int(channel_data.get('viewCount', 0)),
                    int(channel_data.get('subscriberCount', 0)), 
                    int(channel_data.get('videoCount', 0))
                )
            
            logger.info(f"成功将YouTube频道保存到数据库: {channel_data.get('title')}")
            return True
            
        except Exception as e:
            logger.error(f"数据库插入YouTube频道失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def save_channel_stats_snapshot(channel_id, view_count, subscriber_count, video_count):
        """保存频道统计数据快照
        
        Args:
            channel_id (str): YouTube频道ID
            view_count (int): 观看次数
            subscriber_count (int): 订阅者数量
            video_count (int): 视频数量
            
        Returns:
            bool: 操作是否成功
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                INSERT INTO ytb_channel_stats_snapshots (
                    channel_id, view_count, subscriber_count, video_count,
                    snapshot_at, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, NOW(), NOW(), NOW()
                )
            """
            
            cursor.execute(sql, (
                str(channel_id),  # 确保channel_id是字符串类型
                view_count,
                subscriber_count,
                video_count
            ))
            
            conn.commit()
            logger.debug(f"成功保存频道统计快照: 频道ID={channel_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存频道统计快照失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_channel_by_id(channel_id):
        """根据频道ID获取频道信息
        
        Args:
            channel_id (str): YouTube频道ID
        
        Returns:
            dict: 频道信息，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT id,channel_id,title,description,custom_url,published_at,country,view_count,subscriber_count,video_count,thumbnail_url,banner_url,last_refreshed_at,is_verified,created_at,updated_at,deleted_at FROM ytb_channels 
                WHERE channel_id = %s
                AND deleted_at IS NULL
            """
            
            cursor.execute(sql, (channel_id,))
            result = cursor.fetchone()
            
            return result
            
        except Exception as e:
            logger.error(f"查询YouTube频道失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)


    @staticmethod
    def get_channel_by_custom_url(custom_url):
        """根据频道ID获取频道信息
        
        Args:
            custom_url (str): YouTube频道自定义URL
        
        Returns:
            dict: 频道信息，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT id,channel_id,title,description,custom_url,published_at,country,view_count,subscriber_count,video_count,thumbnail_url,banner_url,last_refreshed_at,is_verified,created_at,updated_at,deleted_at FROM ytb_channels 
                WHERE custom_url = %s
                AND deleted_at IS NULL
            """
            
            cursor.execute(sql, (custom_url,))
            result = cursor.fetchone()
            
            return result
            
        except Exception as e:
            logger.error(f"查询YouTube频道失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_channel_by_youtube_id(youtube_id):
        """根据YouTube频道ID获取频道信息
        
        Args:
            youtube_id (str): YouTube频道ID
        
        Returns:
            dict: 频道信息，如果不存在则返回None
        """
        return ChannelDao.get_channel_by_id(youtube_id)
    
    @staticmethod
    def get_all_channels(limit=100, offset=0):
        """获取所有频道
        
        Args:
            limit (int): 限制返回的记录数
            offset (int): 偏移量，用于分页
        
        Returns:
            list: 频道列表
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT id,channel_id,title,description,custom_url,published_at,country,view_count,subscriber_count,video_count,thumbnail_url,banner_url,last_refreshed_at,is_verified,created_at,updated_at,deleted_at FROM ytb_channels 
                WHERE deleted_at IS NULL
                ORDER BY subscriber_count DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (limit, offset))
            results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询YouTube频道列表失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def get_latest_snapshot_date(channel_id):
        """获取指定频道的最新快照日期
        
        Args:
            channel_id (str): YouTube频道ID
        
        Returns:
            date: 最新快照的日期，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            sql = """
                SELECT MAX(snapshot_at)::date FROM ytb_channel_stats_snapshots 
                WHERE channel_id = %s
            """
            
            cursor.execute(sql, (channel_id,))
            result = cursor.fetchone()
            
            # 如果没有快照或结果为None，则返回None
            if result and result[0]:
                return result[0]
            else:
                return None
            
        except Exception as e:
            logger.error(f"查询最新频道快照日期失败: {str(e)}")
            return None
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor)
    
    @staticmethod
    def create_table_if_not_exists():
        """创建YouTube频道表和频道统计快照表（如果不存在）"""
        conn = None
        cursor = None
        try:
            db_pool = DatabasePool()
            conn = db_pool.get_connection()
            cursor = conn.cursor()
            
            # 创建频道表
            sql_channel = """
                CREATE TABLE IF NOT EXISTS ytb_channels (
                    id BIGSERIAL PRIMARY KEY,
                    channel_id VARCHAR(50) NOT NULL UNIQUE,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    custom_url VARCHAR(100),
                    published_at TIMESTAMPTZ,
                    country VARCHAR(2),
                    topic_categories TEXT[],
                    view_count BIGINT,
                    subscriber_count INTEGER,
                    video_count INTEGER,
                    thumbnail_url TEXT,
                    banner_url TEXT,
                    last_refreshed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_channels_channel_id ON ytb_channels(channel_id);
                CREATE INDEX IF NOT EXISTS idx_ytb_channels_last_refreshed_at ON ytb_channels(last_refreshed_at);
            """
            
            # 创建频道统计快照表
            sql_snapshot = """
                CREATE TABLE IF NOT EXISTS ytb_channel_stats_snapshots (
                    id BIGSERIAL PRIMARY KEY,
                    channel_id VARCHAR(50) NOT NULL REFERENCES ytb_channels(channel_id),
                    view_count BIGINT NOT NULL DEFAULT 0,
                    subscriber_count INTEGER NOT NULL DEFAULT 0,
                    video_count INTEGER NOT NULL DEFAULT 0,
                    snapshot_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    deleted_at TIMESTAMPTZ
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_ytb_channel_stats_channel_id_snapshot_at 
                ON ytb_channel_stats_snapshots(channel_id, snapshot_at);
            """
            
            cursor.execute(sql_channel)
            cursor.execute(sql_snapshot)
            
            conn.commit()
            logger.info("YouTube频道表和统计快照表创建成功或已存在")
            return True
            
        except Exception as e:
            logger.error(f"创建YouTube频道相关表失败: {str(e)}")
            return False
        finally:
            if conn and cursor:
                DatabasePool.close_connection(conn, cursor) 