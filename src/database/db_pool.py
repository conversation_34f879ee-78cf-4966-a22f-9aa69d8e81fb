import psycopg2
from psycopg2.pool import SimpleConnectionPool
from psycopg2.extras import DictCursor
import sys
import os
from pathlib import Path
from urllib.parse import urlparse

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from dotenv import load_dotenv
from ..utils.logger import get_logger

# 加载环境变量
load_dotenv()

class DatabasePool:
    _instance = None
    _pool = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabasePool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if DatabasePool._pool is None:
            self.log = get_logger('database')
            self.config = self._get_db_config()
            self._create_pool()
    
    def _get_db_config(self):
        """从环境变量获取数据库配置"""
        try:
            # 优先使用 DATABASE_URL 连接字符串
            database_url = os.getenv('DATABASE_URL')
            if database_url:
                # 解析连接字符串
                url = urlparse(database_url)
                
                # 创建基本配置
                config = {
                    'host': url.hostname,
                    'port': url.port or 5432,
                    'user': url.username,
                    'password': url.password,
                    'dbname': url.path.lstrip('/'),
                    'options': '-c timezone=Asia/Shanghai'  # 设置时区
                }
                
                # 明确设置 sslmode 参数，确保安全连接
                if 'sslmode=require' in database_url:
                    config['sslmode'] = 'require'
                
                self.log.debug(f"使用 DATABASE_URL 配置数据库连接，主机: {config['host']}, 用户: {config['user']}, 数据库: {config['dbname']}, SSL模式: {config.get('sslmode', 'prefer')}")
                return config
            
            # 回退到单独的配置项
            config = {
                'host': os.getenv('DB_HOST'),
                'port': int(os.getenv('DB_PORT', '5432')),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD'),
                'dbname': os.getenv('DB_NAME'),
                'options': '-c timezone=Asia/Shanghai'  # 设置时区
            }
            
            # 检查是否需要SSL连接
            if os.getenv('DB_SSL_REQUIRED') == 'true':
                config['sslmode'] = 'require'
            
            # 验证所有必需的配置
            missing = [k for k, v in config.items() if not v and k != 'options' and k != 'sslmode']
            if missing:
                raise ValueError(f"缺少数据库配置: {', '.join(missing)}")
            
            return config
        
        except Exception as e:
            self.log.error(f"数据库配置错误: {str(e)}")
            raise
    
    def _create_pool(self):
        """创建数据库连接池"""
        try:
            DatabasePool._pool = SimpleConnectionPool(
                minconn=int(os.getenv('DB_MIN_CONNECTIONS', '2')),
                maxconn=int(os.getenv('DB_MAX_CONNECTIONS', '10')),
                **self.config
            )
            self.log.debug("数据库连接池创建成功")
        except Exception as e:
            error_msg = f"创建数据库连接池失败: {str(e)}"
            self.log.error(error_msg)
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = DatabasePool._pool.getconn()
            # 设置时区为Asia/Shanghai
            with conn.cursor() as cur:
                cur.execute("SET timezone = 'Asia/Shanghai'")
            return conn
        except Exception as e:
            logger = get_logger('database')
            logger.error(f"获取数据库连接失败: {str(e)}")
            raise

    def get_new_connection(self):
        """获取一个新的数据库连接（不经过连接池）"""
        try:
            # 创建新连接时设置autocommit=True
            conn = psycopg2.connect(**self.config)
            conn.autocommit = True
            
            # 设置时区为Asia/Shanghai
            with conn.cursor() as cur:
                cur.execute("SET timezone = 'Asia/Shanghai'")
                conn.commit()  # 确保时区设置被提交
            
            return conn
        except Exception as e:
            logger = get_logger('database')
            logger.error(f"获取新数据库连接失败: {str(e)}")
            raise

    def return_connection(self, conn):
        """归还连接到连接池"""
        DatabasePool._pool.putconn(conn)

    @staticmethod
    def close_connection(conn, cursor=None):
        """关闭数据库连接"""
        try:
            if cursor:
                cursor.close()
            if conn:
                DatabasePool._instance.return_connection(conn)
        except Exception as e:
            logger = get_logger('database')
            logger.error(f"关闭数据库连接失败: {str(e)}")
    
    @property
    def get_dict_cursor(self):
        """返回DictCursor类，用于创建返回字典结果的游标"""
        return DictCursor

    def execute_query(self, sql, params=None):
        """执行查询并返回结果"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, params or ())
            return cursor.fetchall()
        finally:
            self.close_connection(conn, cursor)

    def execute_update(self, sql, params=None):
        """执行更新操作"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(sql, params or ())
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            if conn:
                conn.rollback()
            raise
        finally:
            self.close_connection(conn, cursor)

    def get_dict_cursor_connection(self):
        """获取返回字典结果的数据库连接"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)
        return conn, cursor 
    
    def init_clients(self):
        """初始化数据库结构和配置"""
        try:
            # 初始化数据库表结构
            from src.dao.youtube_dao import YouTubeDao
            from src.dao.channel_dao import ChannelDao
            from src.dao.task_dao import TaskDao
            from src.dao.raw_fetch_dao import RawFetchDao
            
            # 创建所有表结构
            TaskDao.create_table_if_not_exists()
            RawFetchDao.create_table_if_not_exists()
            ChannelDao.create_table_if_not_exists()
            YouTubeDao.create_table_if_not_exists()
            
            self.log.info("数据库初始化成功")
            
            # 不再初始化Notion客户端
            return True
            
        except Exception as e:
            self.log.error(f"数据库初始化失败: {e}")
            raise