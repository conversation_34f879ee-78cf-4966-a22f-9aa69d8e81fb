import requests
import os
from fastapi import HTTPException
from typing import Dict, Any
from ..utils.logger import get_logger
from ..dao.channel_dao import ChannelDao
from ..dao.task_dao import TaskDao
from ..dao.raw_fetch_dao import RawFetchDao
from datetime import date

# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")

# 设置日志
logger = get_logger('channel_service')

def get_video_info(video_id: str) -> Dict[str, Any]:
    """获取视频信息"""
    url = "https://www.googleapis.com/youtube/v3/videos"
    params = {
        "key": YOUTUBE_API_KEY,
        "id": video_id,
        "part": "snippet,statistics"
    }
    
    response = requests.get(url, params=params)
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
    
    data = response.json()
    if not data.get("items"):
        raise HTTPException(status_code=404, detail=f"未找到视频: {video_id}")
    
    return data["items"][0]

def get_channel_info(channel_id: str) -> Dict[str, Any]:
    """获取频道信息"""
    # 创建频道信息获取任务
    task_params = {"channel_id": channel_id, "fetch_type": "channel_info"}
    task_id = TaskDao.create_task("channel_info", task_params)
    
    # 更新任务状态为运行中
    TaskDao.update_task_status(task_id, "running")
    
    url = "https://www.googleapis.com/youtube/v3/channels"
    params = {
        "key": YOUTUBE_API_KEY,
        "id": channel_id,
        "part": "snippet,statistics,brandingSettings"
    }
    
    response = requests.get(url, params=params)
    if response.status_code != 200:
        TaskDao.update_task_status(task_id, "failed", error_message=f"YouTube API请求失败: {response.text}")
        raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
    
    data = response.json()
    
    # 保存原始数据
    endpoint = f"channels?part=snippet,statistics,brandingSettings&id={channel_id}"
    RawFetchDao.save_raw_data(
        task_id,
        endpoint,
        data,
        params
    )
    
    if not data.get("items"):
        error_msg = f"未找到频道: {channel_id}"
        TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
        raise HTTPException(status_code=404, detail=error_msg)
    
    channel = data["items"][0]
    
    # 获取品牌设置中的封面图
    banner_url = None
    if 'brandingSettings' in channel and 'image' in channel['brandingSettings']:
        banner_url = channel['brandingSettings']['image'].get('bannerExternalUrl', None)
    
    # 获取缩略图
    thumbnail_url = None
    if 'snippet' in channel and 'thumbnails' in channel['snippet']:
        thumbnails = channel['snippet']['thumbnails']
        for quality in ['high', 'medium', 'default']:
            if quality in thumbnails:
                thumbnail_url = thumbnails[quality]['url']
                break
    
    # 准备保存到数据库的频道信息
    channel_info = {
        'id': channel['id'],
        'title': channel['snippet']['title'],
        'description': channel['snippet']['description'],
        'customUrl': channel['snippet'].get('customUrl', ''),
        'publishedAt': channel['snippet']['publishedAt'],
        'viewCount': int(channel['statistics'].get('viewCount', 0)),
        'subscriberCount': int(channel['statistics'].get('subscriberCount', 0)),
        'videoCount': int(channel['statistics'].get('videoCount', 0)),
        'country': channel['snippet'].get('country', ''),
        'thumbnailUrl': thumbnail_url,
        'bannerUrl': banner_url,
        'isVerified': False  # 这个信息API无法直接获取
    }
    
    # 保存到数据库
    try:
        ChannelDao.save_channel(channel_info)
        logger.info(f'成功将频道信息保存到数据库: {channel_info["title"]}')
        
        # 更新任务状态为成功
        TaskDao.update_task_status(task_id, "success", result={
            "title": channel_info["title"],
            "subscriberCount": channel_info["subscriberCount"]
        })
    except Exception as e:
        logger.error(f"保存频道信息到数据库失败: {str(e)}")
        TaskDao.update_task_status(task_id, "failed", error_message=f"保存频道信息失败: {str(e)}")
    
    return channel 


def update_channel_info_daily():
    """更新频道信息-每日更新"""
    channels = ChannelDao.get_all_channels()
    try:
        for channel in channels:
            get_channel_info(channel['channel_id'])
    except Exception as e:
        logger.error(f"更新频道信息失败: {str(e)}")
        return False
    return True
        
        
def update_channel_info_snapshot():
    """更新频道信息-快照
       # 1. 检查频道快照当天数据是否有
       # 2. 如果有，则不更新
       # 3. 如果没有，则更新快照数据  
    """
    try:
        channels = ChannelDao.get_all_channels()
        for channel in channels:
            channel_id = channel['channel_id']
            # 1. 检查频道快照当天数据是否有
            latest_snapshot_date = ChannelDao.get_latest_snapshot_date(channel['channel_id'])
            today = date.today()
            if latest_snapshot_date == today:
                logger.info(f"频道 {channel['channel_id']} 今日快照已存在，无需更新。")
                continue
            if channel['channel_id'] == 'TESTUCQOt3FI6FBeYecatZq7auyA':
                continue
            # 3. 如果没有或已过期，则更新快照数据  
            logger.info(f"频道 {channel['channel_id']} 今日快照不存在或已过期 ({latest_snapshot_date})，开始更新...")
            # get_channel_info 会获取最新数据并保存，包括调用 save_channel_stats_snapshot
            channel_info = get_channel_info(channel['channel_id']) 
            
            # get_channel_info 成功时返回频道信息字典，失败时会抛出 HTTPException
            # 因此，如果代码执行到这里，说明 API 调用和保存（包括快照）已成功
            logger.info(f"频道 {channel_id} 快照更新成功。")
        
        return True # 操作成功
    except HTTPException as http_exc:
         logger.error(f"更新频道 {channel_id} 快照时获取信息失败: {http_exc.detail}")
         # API 或查找失败，get_channel_info 内部已处理 Task 状态
         return False # 操作失败
    except Exception as e:
        # 其他潜在错误，例如数据库连接问题等
        logger.error(f"更新频道 {channel_id} 快照时发生内部错误: {str(e)}")
        # 可以在这里尝试更新 Task 状态为 failed，如果 task_id 可获取的话
        return False # 操作失败