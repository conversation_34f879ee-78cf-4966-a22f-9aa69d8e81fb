from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
from ..utils.channel_stats_updater import ChannelStatsUpdater
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('statistics_service')

def update_channel_statistics(channel_id: Optional[str] = None, target_date: Optional[datetime] = None) -> Dict[str, Any]:
    """
    更新频道统计数据
    
    Args:
        channel_id: 要处理的频道ID，为None时处理所有频道
        target_date: 要处理的日期，为None时处理昨天的数据
        
    Returns:
        Dict[str, Any]: 统计更新结果
    """
    try:
        if channel_id:
            logger.info(f"手动触发频道 {channel_id} 的统计数据更新")
            success = ChannelStatsUpdater.update_channel_statistics(channel_id, target_date)
        else:
            logger.info("手动触发所有频道的统计数据更新")
            success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
        
        return {
            "success": success,
            "message": "统计数据更新成功" if success else "统计数据更新失败，请查看日志获取详细信息",
            "target_date": target_date.strftime('%Y-%m-%d') if target_date else (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "channel_id": channel_id if channel_id else "all"
        }
    except Exception as e:
        logger.error(f"更新统计数据失败: {str(e)}")
        return {
            "success": False,
            "message": f"更新统计数据失败: {str(e)}",
            "target_date": target_date.strftime('%Y-%m-%d') if target_date else (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "channel_id": channel_id if channel_id else "all"
        } 