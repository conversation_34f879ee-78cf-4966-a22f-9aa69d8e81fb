import subprocess
import sys
import json
import os

# 修复导入路径问题
# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
root_dir = os.path.dirname(current_dir)
# 将项目根目录添加到Python的模块搜索路径
sys.path.insert(0, root_dir)

# import psycopg2 # Removed direct psycopg2 import
# from psycopg2 import sql # Removed direct psycopg2 import
from dotenv import load_dotenv
from datetime import datetime, timedelta
from src.dao.youtube_dao import YouTubeDao # Use the DAO
from src.dao.channel_dao import ChannelDao

# 加载 .env 文件中的环境变量
load_dotenv()

def parse_duration(duration_str):
    """将 yt-dlp 的 duration (秒) 转换为 INTERVAL 类型可接受的字符串。"""
    if duration_str is None:
        return None
    try:
        # Handle potential float values for duration
        seconds = float(duration_str)
        # Ensure we store integer seconds if the duration is whole
        if seconds.is_integer():
            seconds = int(seconds)
            return timedelta(seconds=seconds)
        else:
            # If duration has fractions, decide how to handle: round, truncate, or store precise interval?
            # For now, let's truncate to the nearest second. Adjust if needed.
            print(f"警告: 视频时长 {duration_str} 包含小数秒，将截断处理。", file=sys.stderr)
            return timedelta(seconds=int(seconds))
    except (ValueError, TypeError):
        print(f"警告: 无法解析时长 '{duration_str}'，将设置为 None。", file=sys.stderr)
        return None

def parse_upload_date(date_str):
    """将 yt-dlp 的 upload_date (YYYYMMDD) 转换为 datetime 对象。"""
    if date_str is None:
        return None
    try:
        return datetime.strptime(date_str, '%Y%m%d')
    except (ValueError, TypeError):
        print(f"警告: 无法解析上传日期 '{date_str}'，将设置为 None。", file=sys.stderr)
        return None

# Removed get_db_connection function
# Removed ensure_channel_exists function (will use DAO method)

def process_and_save_video_via_dao(youtube_dao: YouTubeDao, channel_dao: ChannelDao, video_json_str: str):
    """解析单条视频 JSON 数据并使用 DAO 保存到数据库。"""
    try:
        data = json.loads(video_json_str)
    except json.JSONDecodeError as e:
        print(f"错误: 解析 JSON 失败: {e}\n原始数据: {video_json_str[:200]}...", file=sys.stderr)
        return False # 表示处理失败

    youtube_id = data.get('id')
    if not youtube_id:
        print(f"警告: 视频缺少 ID，跳过。数据: {video_json_str[:200]}...", file=sys.stderr)
        return False

    # 对于单个视频，channel_id 可能在 'channel_id' 或 'uploader_id' 字段
    channel_id = data.get('channel_id') or data.get('uploader_id')
    # channel_title = data.get('channel') or data.get('uploader') # Get channel title as well

    if not channel_id:
         print(f"警告: 视频 {youtube_id} 缺少 channel_id/uploader_id，无法确定频道，跳过保存。", file=sys.stderr)
         # Decide if you want to save videos without channel info, maybe log them differently
         return False # Returning False as channel association is usually important

    # categoryId might be in 'categories' list or a single 'category' field
    category_info = data.get('categories')
    category_id = category_info[0] if isinstance(category_info, list) and category_info else data.get('category')

    video_data = {
        'id': youtube_id,
        'channelId': channel_id,
        'title': data.get('title'),
        'description': data.get('description'),
        'publishedAt': parse_upload_date(data.get('upload_date')),
        'duration': parse_duration(data.get('duration')),
        'tags': data.get('tags'),
        'categoryId': category_id, # Use extracted category
        'thumbnailUrl': data.get('thumbnail'),
        'thumbnails': data.get('thumbnails'), # Often a list of different sizes
        'viewCount': data.get('view_count'),
        'likeCount': data.get('like_count') if data.get('like_count') is not None else 0,
        'commentCount': data.get('comment_count') if data.get('comment_count') is not None else 0,
        'lastRefreshedAt': datetime.now(),
        # 其他字段根据需要添加或通过 API 补充
        'averageRating': data.get('average_rating'),
        'liveStatus': data.get('live_status'), # e.g., 'not_live', 'is_live', 'was_live'
        'webpageUrl': data.get('webpage_url'), # The original URL
        'originalUrl': data.get('original_url'), # May differ slightly
        'isFamilyFriendly': data.get('age_limit') == 0 if data.get('age_limit') is not None else None, # Infer from age_limit
        # Add more fields from yt-dlp output as needed
    }

    try:
        # 1. 确保频道存在 (调用 DAO 方法)
        channel = channel_dao.get_channel_by_youtube_id(channel_id)
        if channel is None:
            # Try to get channel title if available in video data
            channel_title = data.get('channel') or data.get('uploader') or f"Unknown Channel ({channel_id})"
            print(f"信息: 频道 {channel_id} ('{channel_title}') 不存在于数据库，将尝试创建。", file=sys.stderr)
            # You might need a specific DAO method to create a channel if it doesn't exist
            # Assuming ChannelDao has an add_channel method
            try:
                # Pass necessary info, adjust according to your ChannelDao.add_channel signature
                channel_data = {'id': channel_id, 'title': channel_title, 'description': None, 'customUrl': None, 'publishedAt': None, 'thumbnailUrl': None, 'viewCount': None, 'subscriberCount': None, 'videoCount': None, 'country': None} # Add more fields if needed
                channel_dao.add_channel(channel_data)
                print(f"频道 {channel_id} 已创建。")
            except Exception as ch_e:
                print(f"错误: 尝试创建频道 {channel_id} 失败: {ch_e}", file=sys.stderr)
                return False # Fail saving video if channel creation fails

        # 2. 保存视频数据 (调用 DAO 方法, assuming it handles UPSERT)
        youtube_dao.save_video(video_data)
        print(f"成功处理并保存视频: {youtube_id} ({video_data.get('title', 'N/A')})")
        return True # 表示处理成功

    except Exception as e:
        # Catching a broad exception here, specific exceptions depend on DAO implementation
        print(f"数据库操作错误 (处理视频 {youtube_id} via DAO): {e}", file=sys.stderr)
        # Log the data that caused the error for debugging
        print(f"失败的数据: {video_data}", file=sys.stderr) # More detailed logging
        return False # 表示处理失败

# --- 新函数：处理单个视频 ---
def get_single_video_json_and_save(video_url, youtube_dao: YouTubeDao, channel_dao: ChannelDao):
    """
    使用 yt-dlp 获取单个视频的 JSON 数据，解析并使用 YouTubeDao 保存到数据库。
    """
    # 确保 yt-dlp 命令存在 (与原函数相同)
    try:
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True, text=True)
    except FileNotFoundError:
        print("错误: 'yt-dlp' 命令未找到或无法执行。", file=sys.stderr)
        print("请确保 yt-dlp 已经安装并且在您的系统 PATH 环境变量中。", file=sys.stderr)
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"错误: 执行 'yt-dlp --version' 失败: {e}", file=sys.stderr)
        sys.exit(1)

    # 构建 yt-dlp 命令获取单个视频的 JSON
    command = [
        'yt-dlp',
        '--skip-download',
        '-j',               # 输出 JSON 格式
        '--no-playlist',    # 确保只处理指定的视频 URL，即使它属于某个播放列表
        '--ignore-errors',
        video_url           # 单个视频 URL
    ]

    command_str = ' '.join(command)
    print(f"即将执行命令: {command_str}")
    print("-" * 30)

    saved = False

    try:
        # 执行命令并捕获输出
        result = subprocess.run(
            command,
            capture_output=True, # 捕获 stdout 和 stderr
            text=True,           # 使用文本模式
            check=False          # 不自动检查返回码，手动处理
        )

        # 检查 stderr for errors from yt-dlp
        if result.stderr:
            print("\n----- yt-dlp 错误/日志 (stderr) ----- ")
            print(result.stderr.strip(), file=sys.stderr) # Print stderr to stderr
            print("----- /yt-dlp 错误/日志 (stderr) ----- ")

        # 检查返回码
        if result.returncode != 0:
            print(f"警告: yt-dlp 命令执行失败，返回码: {result.returncode}", file=sys.stderr)
            # Decide if you want to proceed or exit based on the error
            # sys.exit(1) # Optionally exit if yt-dlp fails

        # 处理 stdout (JSON 数据)
        if result.stdout:
            video_json = result.stdout.strip()
            print("\n----- RAW JSON from yt-dlp (within script) -----")
            print(video_json) # 打印原始 JSON
            print("----- /RAW JSON ----- \n")
            if video_json:
                print("----- 开始处理 yt-dlp 输出并存入数据库 (via DAO) ----- ")
                if process_and_save_video_via_dao(youtube_dao, channel_dao, video_json):
                    saved = True
                else:
                    print(f"未能成功保存视频 {video_url} 的数据。", file=sys.stderr)
                print("----- yt-dlp 输出处理完毕 ----- ")
            else:
                print("警告: yt-dlp 未返回任何 JSON 数据。尝试使用 cookie...", file=sys.stderr)                

        else:
            print("警告: yt-dlp 未在 stdout 中输出任何内容。", file=sys.stderr)
            cookie_file_path = '/tmp/yt_cookies.txt'
            if os.path.exists(cookie_file_path):
                print(f"----- 检测到 cookie 文件: {cookie_file_path}，尝试使用它 ----- ")
                command_with_cookie = [
                    'yt-dlp',
                    '--skip-download',
                    '-j',
                    '--no-playlist',
                    '--cookies', cookie_file_path,
                    '--ignore-errors',
                    video_url
                ]
                command_with_cookie_str = ' '.join(command_with_cookie)
                print(f"即将执行带 cookie 的命令: {command_with_cookie_str}")

                try:
                    result_with_cookie = subprocess.run(
                        command_with_cookie,
                        capture_output=True,
                        text=True,
                        check=False
                    )

                    # 检查带 cookie 的 stderr
                    if result_with_cookie.stderr:
                        print("\n----- yt-dlp (with cookie) 错误/日志 (stderr) ----- ")
                        print(result_with_cookie.stderr.strip(), file=sys.stderr)
                        print("----- /yt-dlp (with cookie) 错误/日志 (stderr) ----- ")

                    # 检查带 cookie 的返回码
                    if result_with_cookie.returncode != 0:
                        print(f"警告: 带 cookie 的 yt-dlp 命令执行失败，返回码: {result_with_cookie.returncode}", file=sys.stderr)

                    # 处理带 cookie 的 stdout
                    if result_with_cookie.stdout:
                        video_json_cookie = result_with_cookie.stdout.strip()
                        print("\n----- RAW JSON from yt-dlp (with cookie) -----")
                        print(video_json_cookie)
                        print("----- /RAW JSON (with cookie) ----- \n")
                        if video_json_cookie:
                            print("----- 开始处理 yt-dlp (with cookie) 输出并存入数据库 (via DAO) ----- ")
                            if process_and_save_video_via_dao(youtube_dao, channel_dao, video_json_cookie):
                                saved = True
                            else:
                                print(f"未能成功保存视频 {video_url} 的数据 (使用 cookie)。", file=sys.stderr)
                            print("----- yt-dlp (with cookie) 输出处理完毕 ----- ")
                        else:
                            print("警告: 使用 cookie 的 yt-dlp 未返回任何 JSON 数据。", file=sys.stderr)
                    else:
                        print("警告: 使用 cookie 的 yt-dlp 未在 stdout 中输出任何内容。", file=sys.stderr)

                except Exception as cookie_e:
                    print(f"使用 cookie 执行 yt-dlp 时发生错误: {cookie_e}", file=sys.stderr)
        print(f"\n处理总结: {'成功' if saved else '失败'} 处理视频 {video_url}。")

    except Exception as e:
        print(f"\n执行过程中发生意外错误: {e}", file=sys.stderr)
        print(f"\n处理总结 (失败): 处理视频 {video_url} 时出错。")
        sys.exit(1)
    finally:
        print("脚本执行完毕。")


def get_channel_videos_json_and_save(channel_url):
    """
    使用 yt-dlp 获取频道所有视频的 JSON 数据，解析并使用 YouTubeDao 保存到数据库。
    (此函数保持不变，以备后用)
    """
    # 确保 yt-dlp 命令存在
    try:
        # 尝试获取 yt-dlp 版本信息，如果命令不存在会抛出 FileNotFoundError
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True, text=True)
    except FileNotFoundError:
        print("错误: 'yt-dlp' 命令未找到或无法执行。", file=sys.stderr)
        print("请确保 yt-dlp 已经安装并且在您的系统 PATH 环境变量中。", file=sys.stderr)
        sys.exit(1) # 退出脚本
    except subprocess.CalledProcessError as e:
        print(f"错误: 执行 'yt-dlp --version' 失败: {e}", file=sys.stderr)
        sys.exit(1)

    # 构建 yt-dlp 命令
    command = [
        'yt-dlp',
        '--skip-download',
        '-j',
        # '--flat-playlist', # 如果需要完整数据则注释掉
        '--ignore-errors',
        channel_url
    ]

    command_str = ' '.join(command)
    print(f"即将执行命令: {command_str}")
    print("-" * 30)

    saved_count = 0
    failed_count = 0
    processed_count = 0

    youtube_dao = None
    channel_dao = None
    try:
        # 实例化 DAO
        youtube_dao = YouTubeDao()
        channel_dao = ChannelDao()
        print("DAO 实例化成功。")

        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        print("----- 开始处理 yt-dlp 输出并存入数据库 (via DAO) ----- ")
        if process.stdout:
            for line in process.stdout:
                processed_count += 1
                if line.strip():
                    if process_and_save_video_via_dao(youtube_dao, channel_dao, line):
                        saved_count += 1
                    else:
                        failed_count += 1

                if processed_count % 10 == 0:
                     print(f"已处理: {processed_count}, 成功保存: {saved_count}, 失败: {failed_count}", end='\r')

        print("\n----- yt-dlp 输出处理完毕 ----- ")

        stderr_output = ""
        if process.stderr:
            stderr_output = process.stderr.read()
            if stderr_output:
                print("\n----- yt-dlp 错误/日志 (stderr) ----- ")
                print(stderr_output.strip(), file=sys.stderr) # Print stderr to stderr
                print("----- /yt-dlp 错误/日志 (stderr) ----- ")

        process.wait()
        print("-" * 30)
        if process.returncode == 0:
            print("yt-dlp 命令执行成功。")
        else:
            print(f"警告: yt-dlp 命令执行可能存在问题，返回码: {process.returncode}", file=sys.stderr)

        print(f"\n处理总结: 总计处理 {processed_count} 条记录, 成功保存 {saved_count} 条, 失败 {failed_count} 条。")

    except Exception as e:
        print(f"\n执行过程中发生意外错误: {e}", file=sys.stderr)
        print(f"\n处理总结 (可能不完整): 总计处理 {processed_count} 条记录, 成功保存 {saved_count} 条, 失败 {failed_count} 条。")
        sys.exit(1)
    finally:
        # Optional: Add cleanup for DAOs if needed
        print("脚本执行完毕。")


def update_video_published_at_is_null():
    """
    更新视频发布时间为空的视频数据
    """
    """
    清洗发布时间是空的视频数据
    """
    # --- 清洗发布时间是空的视频数据 ---
    channel_dao = ChannelDao()
    youtube_dao = YouTubeDao() # Need an instance to call the method

    # 1. 获取所有频道
    channels = channel_dao.get_all_channels(100,0)
    for channel in channels:
        print(f"频道 ID: {channel[1]}, 频道标题: {channel[2]}")
        channel_id_to_check = channel[1]
        channel_title_to_check = channel[2]
        print(f"开始获取频道 {channel_title_to_check} 的视频数据...")
      
        # 2. 获取频道内所有视频
        # all_videos = youtube_dao.get_videos_by_channel_id(channel_id=channel_id_to_check,limit=500,offset=0)
        all_videos = youtube_dao.get_video_by_published_at_is_null(channel_id=channel_id_to_check)
        if all_videos:
            print(f"从数据库:{channel_title_to_check}获取到 {len(all_videos)} 条视频数据")
            for video in all_videos:
                print(f"视频 ID: {video[1]}")
                print(f"视频标题: {video[2]}")
                print(f"视频发布时间: {video[5]}")
                target_video_url = f"https://www.youtube.com/watch?v={video[1]}" # 移除时间戳，通常不需要
                print(f"\n开始获取单个视频数据: {target_video_url}")
                # 3. 更新单个视频的数据，18+的视频需要cookie才能更新
                get_single_video_json_and_save(target_video_url, youtube_dao, channel_dao) # Assuming this function handles DAO instantiation
                print("-" * 30)
        else:
            print(f"发布时间为空:未能从数据库获取到频道 {channel_id_to_check} 的视频数据。")

from ..services.channel_service import get_video_info
def update_video_comment_and_like_count_zero():
    """
    更新视频评论量和点赞量都是0的视频数据
    """
    youtube_dao = YouTubeDao()
    channel_dao = ChannelDao()
    # 1. 获取所有频道
    channels = channel_dao.get_all_channels(100,0)
    for channel in channels:
        print(f"频道 ID: {channel[1]}, 频道标题: {channel[2]}")
        channel_id_to_check = channel[1]
        channel_title_to_check = channel[2]
        # if "UCb1oG-4QYwEJ_wLeXda-MhA" not in channel_id_to_check:
        #     continue
        # 频道过滤，部分频道关闭评论功能，导致无法获取
        if 'UCYQo8CdhXD22qfwUBUw591Q' in channel_id_to_check:
            continue

        # 2. 获取频道内所有视频
        print(f"开始获取频道 {channel_title_to_check} 的视频数据...")
        videos = youtube_dao.get_video_by_comment_count_and_like_count_zero(channel_id=channel_id_to_check)
        print(f"获取到 {len(videos)} 条视频数据")
        for video in videos:
            print(f"视频 ID: {video[1]}")
            print(f"视频标题: {video[2]}")
            print(f"视频阅读量: {video[6]}")
            print(f"视频点赞量: {video[7]}")
            target_video_url = f"https://www.youtube.com/watch?v={video[1]}" # 移除时间戳，通常不需要
            print(f"\n开始获取单个视频数据: {target_video_url}")
            # 3.yt-dlp方式： 更新单个视频的数据，18+的视频需要cookie才能更新
            # get_single_video_json_and_save(target_video_url, youtube_dao, channel_dao) # Assuming this function handles DAO instantiation
            
            # 3.api方式： 更新单个视频的数据
            try:
                video_info = get_video_info(video[1])
                statistics = video_info.get("statistics", {})
                view_count = statistics.get("viewCount")
                like_count = statistics.get("likeCount")
                favorite_count = statistics.get("favoriteCount")
                comment_count = statistics.get("commentCount")
                if like_count is None:
                    continue
                video_data = {
                    "youtube_id": video[1],
                    "view_count": view_count,
                    "like_count": like_count,
                    "favorite_count": favorite_count,
                    "comment_count": comment_count
                }
                youtube_dao.update_video(video_data)
                print(f"更新视频数据: {video_data}")
                print("-" * 30)
            except Exception as e:
                print(f"更新视频数据失败: {e}")
                print("-" * 30)
        

def main():
    update_video_published_at_is_null()
    # update_video_comment_and_like_count_zero()

# --- 使用示例 ---
if __name__ == "__main__":
    """
    1. 清洗发布时间是空的视频数据
    """
    main()
    
    # --- 清洗发布时间是空的视频数据 ---
    # channel_dao = ChannelDao()
    # youtube_dao = YouTubeDao() # Need an instance to call the method

    # channels = channel_dao.get_all_channels(100,0)
    # for channel in channels:
    #     print(f"频道 ID: {channel[1]}, 频道标题: {channel[2]}")
    #     channel_id_to_check = channel[1]
    #     print(f"开始获取频道 {channel_id_to_check} 的视频数据...")
      
    #     all_videos = youtube_dao.get_videos_by_channel_id(channel_id=channel_id_to_check,limit=500,offset=0)

    #     if all_videos:
    #         print(f"从数据库获取到 {len(all_videos)} 条视频数据")

    #         # 过滤 publishedAt 不为 None 的视频
    #         # Assuming get_videos_by_channel_id returns a list of dictionaries or objects with a 'publishedAt' key/attribute
    #         videos_with_published_at = [
    #             video for video in all_videos
    #             if video[5] is None # Use .get() for safety if it's a dict
    #             # Or if it's an object: if video.publishedAt is not None
    #         ]
    #         print(f"过滤后得到 {len(videos_with_published_at)} 条不具有发布时间的视频数据")
    #         # 现在 videos_with_published_at 变量包含了过滤后的数据
    #         # 你可以在这里继续处理 videos_with_published_at

    #         for video in videos_with_published_at:
    #             print(f"视频 ID: {video[1]}")
    #             print(f"视频标题: {video[2]}")
    #             print(f"视频发布时间: {video[5]}")
    #             target_video_url = f"https://www.youtube.com/watch?v={video[1]}" # 移除时间戳，通常不需要
    #             print(f"\n开始获取单个视频数据: {target_video_url}")
    #             # Ensure DAOs are instantiated if get_single_video_json_and_save doesn't do it internally
    #             get_single_video_json_and_save(target_video_url) # Assuming this function handles DAO instantiation
    #             print("-" * 30)
    #     else:
    #         print(f"未能从数据库获取到频道 {channel_id_to_check} 的视频数据。")


    # --- 获取单个视频 ---
    # target_video_url = "https://www.youtube.com/watch?v=JEesSyF8wjc" # 移除时间戳，通常不需要
    # print(f"\n开始获取单个视频数据: {target_video_url}")
    # Ensure DAOs are instantiated if get_single_video_json_and_save doesn't do it internally
    # get_single_video_json_and_save(target_video_url) # Assuming this function handles DAO instantiation

    # --- 获取频道视频 (注释掉，如果只想运行单个视频) ---
    # target_channel_url = "https://www.youtube.com/@lingdujieshuo"
    # print(f"\n开始获取频道视频数据: {target_channel_url}")
    # get_channel_videos_json_and_save(target_channel_url)

    # --- 通过命令行参数获取 ---
    # import argparse
    # parser = argparse.ArgumentParser(description='获取 YouTube 视频或频道 JSON 数据')
    # parser.add_argument('url', help='目标 YouTube 视频或频道的 URL')
    # args = parser.parse_args()
    # target_url = args.url

    # # 判断是视频还是频道 (简单判断，可能需要更复杂的逻辑)
    # if "/watch?" in target_url:
    #      print(f"检测到视频 URL: {target_url}")
    #      get_single_video_json_and_save(target_url)
    # elif "/@" in target_url or "/channel/" in target_url or "/user/" in target_url:
    #      print(f"检测到频道 URL: {target_url}")
    #      get_channel_videos_json_and_save(target_url)
    # else:
    #      print(f"错误: 无法识别的 URL 类型: {target_url}", file=sys.stderr)
    #      sys.exit(1)
