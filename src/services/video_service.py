import os
import uuid
import yt_dlp
from yt_dlp.utils import download_range_func
from ..utils.logger import get_logger
from ..utils.download_utils import get_yt_dlp_opts, TEMP_DIR
from ..utils.youtube_utils import extract_video_id_from_url

# 设置日志
logger = get_logger('video_service')

def download_video(video_url: str, format: str = "best"):
    """下载YouTube视频"""
    try:
        # 创建唯一的下载目录
        download_id = str(uuid.uuid4())
        download_dir = os.path.join(TEMP_DIR, download_id)
        os.makedirs(download_dir, exist_ok=True)
        
        # 提取视频ID
        video_id = extract_video_id_from_url(video_url)
        
        # 设置下载选项
        ydl_opts = get_yt_dlp_opts({
            'format': format,
            'outtmpl': os.path.join(download_dir, '%(title)s.%(ext)s'),
            'quiet': False,
        })
        
        # 下载视频
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
            downloaded_file = ydl.prepare_filename(info)
        
        return {
            "message": "视频下载成功",
            "video_id": video_id,
            "title": info.get('title'),
            "download_path": downloaded_file,
            "download_id": download_id
        }
    except Exception as e:
        logger.error(f"下载视频失败: {str(e)}")
        raise e

def download_video_clip(video_url: str, start_time: float, end_time: float, format: str = "best[ext=mp4]"):
    """下载YouTube视频片段"""
    try:
        # 创建唯一的下载目录
        download_id = str(uuid.uuid4())
        download_dir = os.path.join(TEMP_DIR, download_id)
        os.makedirs(download_dir, exist_ok=True)
        
        # 提取视频ID
        video_id = extract_video_id_from_url(video_url)
        
        # 设置下载选项，包括时间范围
        ydl_opts = get_yt_dlp_opts({
            'format': format,
            'outtmpl': os.path.join(download_dir, '%(title)s_clip.%(ext)s'),
            'quiet': False,
            'download_ranges': download_range_func(None, [(start_time, end_time)]),
            'force_keyframes_at_cuts': True,
        })
        
        # 下载视频片段
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
            downloaded_file = ydl.prepare_filename(info)
        
        return {
            "message": "视频片段下载成功",
            "video_id": video_id,
            "title": info.get('title'),
            "clip_start": start_time,
            "clip_end": end_time,
            "download_path": downloaded_file,
            "download_id": download_id
        }
    except Exception as e:
        logger.error(f"下载视频片段失败: {str(e)}")
        raise e

def download_audio(video_url: str, format: str = "bestaudio[ext=m4a]"):
    """下载YouTube视频的音频"""
    try:
        # 创建唯一的下载目录
        download_id = str(uuid.uuid4())
        download_dir = os.path.join(TEMP_DIR, download_id)
        os.makedirs(download_dir, exist_ok=True)
        
        # 提取视频ID
        video_id = extract_video_id_from_url(video_url)
        
        # 设置下载选项
        ydl_opts = get_yt_dlp_opts({
            'format': format,
            'outtmpl': os.path.join(download_dir, '%(title)s.%(ext)s'),
            'quiet': False,
        })
        
        # 下载音频
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
            downloaded_file = ydl.prepare_filename(info)
        
        return {
            "message": "音频下载成功",
            "video_id": video_id,
            "title": info.get('title'),
            "download_path": downloaded_file,
            "download_id": download_id
        }
    except Exception as e:
        logger.error(f"下载音频失败: {str(e)}")
        raise e 