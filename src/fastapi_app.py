# from fastapi import FastAPI, HTTPException, Query, Body, BackgroundTasks
# import os
# import re
# import sys
# from pathlib import Path
# import uuid
# from fastapi.middleware.cors import CORSMiddleware
# # 添加项目根目录到 Python 路径
# project_root = str(Path(__file__).parent.parent)
# if project_root not in sys.path:
#     sys.path.append(project_root)

# import requests
# from dotenv import load_dotenv
# from pydantic import BaseModel, Field, HttpUrl
# from typing import Optional, Dict, Any, List
# from utils.logger import get_logger
# import uvicorn
# from main import setup_database, setup_logging_from_config
# from uuid import uuid4
# import yt_dlp
# from yt_dlp.utils import download_range_func
# import shutil
# import time
# import asyncio
# import random
# from datetime import datetime, timedelta
# from .utils.channel_stats_updater import ChannelStatsUpdater

# # 导入所需的DAO类
# from dao.channel_dao import ChannelDao
# from dao.task_dao import TaskDao
# from dao.raw_fetch_dao import RawFetchDao
# from dao.youtube_dao import YouTubeDao

# # 加载环境变量
# load_dotenv()

# # 设置日志
# logger = get_logger('fastapi_app')

# # 从环境变量获取YouTube API密钥
# YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")

# if not YOUTUBE_API_KEY:
#     logger.error("未配置YouTube API密钥，请在.env文件中设置YOUTUBE_API_KEY")
#     sys.exit(1)

# # 创建FastAPI应用
# app = FastAPI(
#     title="YouTube API",
#     description="用于查询YouTube频道信息的API",
#     version="1.0.0",
# )

# # 添加CORS中间件
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],  # 允许所有来源，生产环境建议配置具体的来源列表
#     allow_credentials=True,
#     allow_methods=["GET", "POST", "PUT", "OPTIONS"],  # 允许所有方法
#     allow_headers=["*"],  # 允许所有头部
# )

# # 定义响应模型
# class ChannelResponse(BaseModel):
#     channel_id: str = Field(..., description="YouTube频道ID")
#     title: Optional[str] = Field(None, description="频道标题")
#     description: Optional[str] = Field(None, description="频道描述")
#     custom_url: Optional[str] = Field(None, description="自定义URL")
#     thumbnail_url: Optional[str] = Field(None, description="频道缩略图URL")
#     subscriber_count: Optional[int] = Field(None, description="订阅者数量")
#     video_count: Optional[int] = Field(None, description="视频数量")
#     view_count: Optional[int] = Field(None, description="总观看次数")
#     published_at: Optional[str] = Field(None, description="频道创建时间")
#     channel_url: str = Field(..., description="频道URL")

# class ErrorResponse(BaseModel):
#     error: str = Field(..., description="错误信息")

# # 辅助函数：从视频URL或ID中提取视频ID
# def extract_video_id_from_url(video_url_or_id: str) -> str:
#     """从视频URL或ID中提取视频ID"""
#     # 如果已经是一个简单的视频ID（11个字符的字母数字），直接返回
#     if re.match(r'^[a-zA-Z0-9_-]{11}$', video_url_or_id):
#         return video_url_or_id
    
#     # 尝试从URL中提取视频ID
#     youtube_regex = (
#         r'(?:youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=)|youtu\.be\/|youtube.com\/shorts\/)([^&?\n]+)'
#     )
#     match = re.search(youtube_regex, video_url_or_id)
#     if match:
#         return match.group(1)
    
#     raise ValueError("无法从提供的URL或ID中提取视频ID")

# # 辅助函数：从频道URL或ID中提取频道ID
# def extract_channel_id(channel_url_or_id: str) -> str:
#     """从频道URL或ID中提取频道ID"""
#     # 如果已经是一个频道ID（以UC开头），直接返回
#     if re.match(r'^UC[a-zA-Z0-9_-]+$', channel_url_or_id):
#         return channel_url_or_id
    
#     # 检查是否是频道URL
#     channel_regex = r'(?:youtube\.com\/channel\/)([^\/\n]+)'
#     match = re.search(channel_regex, channel_url_or_id)
#     if match:
#         return match.group(1)
    
#     # 检查是否是自定义URL (@username)
#     custom_url_regex = r'(?:youtube\.com\/@)([^\/\n]+)'
#     match = re.search(custom_url_regex, channel_url_or_id)
#     if match:
#         # 需要通过API查询获取真实频道ID
#         username = match.group(1)
#         return get_channel_id_from_username(username)
    
#     # 检查是否直接使用@username格式（不含URL）
#     if channel_url_or_id.startswith('@'):
#         # 去掉@前缀，获取用户名
#         username = channel_url_or_id[1:]
#         return get_channel_id_from_username(username)
    
#     # raise ValueError("无法从提供的URL或ID中提取频道ID")
#     return None

# # 辅助函数：从用户名获取频道ID
# def get_channel_id_from_username(username: str) -> str:
#     """从用户名获取频道ID"""
#     url = "https://www.googleapis.com/youtube/v3/channels"
#     params = {
#         "key": YOUTUBE_API_KEY,
#         "forUsername": username,
#         "part": "id"
#     }
    
#     response = requests.get(url, params=params)
#     if response.status_code != 200:
#         raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
    
#     data = response.json()
#     if not data.get("items"):
#         # 如果forUsername搜索不到，尝试搜索
#         return search_channel_by_username(username)
    
#     return data["items"][0]["id"]

# # 辅助函数：通过搜索查找频道
# def search_channel_by_username(username: str) -> str:
#     """通过搜索查找频道ID"""
#     url = "https://www.googleapis.com/youtube/v3/search"
#     params = {
#         "key": YOUTUBE_API_KEY,
#         "q": username,
#         "type": "channel",
#         "part": "id",
#         "maxResults": 1
#     }
    
#     response = requests.get(url, params=params)
#     if response.status_code != 200:
#         raise HTTPException(status_code=response.status_code, detail=f"YouTube API搜索请求失败: {response.text}")
    
#     data = response.json()
#     if not data.get("items"):
#         raise HTTPException(status_code=404, detail=f"未找到匹配的频道: {username}")
    
#     return data["items"][0]["id"]["channelId"]

# # 获取视频信息
# def get_video_info(video_id: str) -> Dict[str, Any]:
#     """获取视频信息"""
#     url = "https://www.googleapis.com/youtube/v3/videos"
#     params = {
#         "key": YOUTUBE_API_KEY,
#         "id": video_id,
#         "part": "snippet"
#     }
    
#     response = requests.get(url, params=params)
#     if response.status_code != 200:
#         raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
    
#     data = response.json()
#     if not data.get("items"):
#         raise HTTPException(status_code=404, detail=f"未找到视频: {video_id}")
    
#     return data["items"][0]

# # 获取频道信息
# def get_channel_info(channel_id: str) -> Dict[str, Any]:
#     """获取频道信息"""
#     # 创建频道信息获取任务
#     task_params = {"channel_id": channel_id, "fetch_type": "channel_info"}
#     task_id = TaskDao.create_task("channel_info", task_params)
    
#     # 更新任务状态为运行中
#     TaskDao.update_task_status(task_id, "running")
    
#     url = "https://www.googleapis.com/youtube/v3/channels"
#     params = {
#         "key": YOUTUBE_API_KEY,
#         "id": channel_id,
#         "part": "snippet,statistics,brandingSettings"
#     }
    
#     response = requests.get(url, params=params)
#     if response.status_code != 200:
#         TaskDao.update_task_status(task_id, "failed", error_message=f"YouTube API请求失败: {response.text}")
#         raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
    
#     data = response.json()
    
#     # 保存原始数据
#     endpoint = f"channels?part=snippet,statistics,brandingSettings&id={channel_id}"
#     RawFetchDao.save_raw_data(
#         task_id,
#         endpoint,
#         data,
#         params
#     )
    
#     if not data.get("items"):
#         error_msg = f"未找到频道: {channel_id}"
#         TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#         raise HTTPException(status_code=404, detail=error_msg)
    
#     channel = data["items"][0]
    
#     # 获取品牌设置中的封面图
#     banner_url = None
#     if 'brandingSettings' in channel and 'image' in channel['brandingSettings']:
#         banner_url = channel['brandingSettings']['image'].get('bannerExternalUrl', None)
    
#     # 获取缩略图
#     thumbnail_url = None
#     if 'snippet' in channel and 'thumbnails' in channel['snippet']:
#         thumbnails = channel['snippet']['thumbnails']
#         for quality in ['high', 'medium', 'default']:
#             if quality in thumbnails:
#                 thumbnail_url = thumbnails[quality]['url']
#                 break
    
#     # 准备保存到数据库的频道信息
#     channel_info = {
#         'id': channel['id'],
#         'title': channel['snippet']['title'],
#         'description': channel['snippet']['description'],
#         'customUrl': channel['snippet'].get('customUrl', ''),
#         'publishedAt': channel['snippet']['publishedAt'],
#         'viewCount': int(channel['statistics'].get('viewCount', 0)),
#         'subscriberCount': int(channel['statistics'].get('subscriberCount', 0)),
#         'videoCount': int(channel['statistics'].get('videoCount', 0)),
#         'country': channel['snippet'].get('country', ''),
#         'thumbnailUrl': thumbnail_url,
#         'bannerUrl': banner_url,
#         'isVerified': False  # 这个信息API无法直接获取
#     }
    
#     # 保存到数据库
#     try:
#         ChannelDao.save_channel(channel_info)
#         logger.info(f'成功将频道信息保存到数据库: {channel_info["title"]}')
        
#         # 更新任务状态为成功
#         TaskDao.update_task_status(task_id, "success", result={
#             "title": channel_info["title"],
#             "subscriberCount": channel_info["subscriberCount"]
#         })
#     except Exception as e:
#         logger.error(f"保存频道信息到数据库失败: {str(e)}")
#         TaskDao.update_task_status(task_id, "failed", error_message=f"保存频道信息失败: {str(e)}")
    
#     return channel

# # 定义请求模型
# class VideoDownloadRequest(BaseModel):
#     video_url: str = Field(..., description="YouTube视频URL或ID")
#     format: str = Field("best", description="视频格式，例如：best, bestvideo+bestaudio, 360p, 720p")

# class VideoClipRequest(BaseModel):
#     video_url: str = Field(..., description="YouTube视频URL或ID")
#     start_time: float = Field(..., description="开始时间（秒）")
#     end_time: float = Field(..., description="结束时间（秒）")
#     format: str = Field("best[ext=mp4]", description="视频格式")

# class AudioDownloadRequest(BaseModel):
#     video_url: str = Field(..., description="YouTube视频URL或ID")
#     format: str = Field("bestaudio[ext=m4a]", description="音频格式")

# # 创建临时目录
# TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_downloads")
# os.makedirs(TEMP_DIR, exist_ok=True)

# # 添加新的请求模型
# class ChannelVideosDateRangeRequest(BaseModel):
#     channel_url: str = Field(..., description="YouTube频道URL、ID或用户名(@username)")
#     start_date: str = Field(..., description="开始日期(YYYYMMDD格式，如20240101)")
#     end_date: str = Field(..., description="结束日期(YYYYMMDD格式，如20240131)")
#     max_videos: int = Field(50, description="最大获取视频数量，默认50")

# # 添加新的请求模型
# class ChannelVideosRequest(BaseModel):
#     channel_url: str = Field(..., description="YouTube频道URL、ID或用户名(@username)")
#     max_videos: int = Field(50, description="最大获取视频数量，默认50")

# # 添加cookie文件路径常量
# COOKIES_FILE = '/tmp/yt_cookies.txt'

# def get_yt_dlp_opts(additional_opts=None):
#     """
#     获取yt-dlp的基础配置
#     """
#     opts = {
#         'quiet': True,
#         'no_warnings': True,
#         'sleep_interval': 2,   # 请求间隔2秒
#         'max_sleep_interval': 5,  # 最大间隔5秒
#         'retries': 10,         # 重试次数
#         'fragment_retries': 10, # 片段重试次数
#         'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
#     }
    
#     # 尝试加载本地cookie文件
#     if os.path.exists(COOKIES_FILE):
#         logger.info(f"使用本地cookie文件: {COOKIES_FILE}")
#         opts['cookiefile'] = COOKIES_FILE
#     else:
#         logger.warning(f"本地cookie文件不存在: {COOKIES_FILE}")
    
#     # 合并额外的配置选项
#     if additional_opts:
#         opts.update(additional_opts)
        
#     return opts

# @app.get("/", include_in_schema=False)
# async def root():
#     return {"message": "欢迎使用YouTube API"}

# @app.get(
#     "/api/channel/from-video",
#     response_model=ChannelResponse,
#     responses={404: {"model": ErrorResponse}, 400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
#     tags=["频道信息"],
#     summary="根据视频URL或ID获取频道信息",
#     description="根据提供的YouTube视频URL或视频ID，返回该视频所属的频道信息"
# )
# async def get_channel_from_video(
#     video_url_or_id: str = Query(..., description="YouTube视频URL或ID")
# ):
#     try:
#         # 从视频URL或ID中提取视频ID
#         video_id = extract_video_id_from_url(video_url_or_id)
        
#         # 获取视频信息
#         video_info = get_video_info(video_id)
        
#         # 从视频信息中获取频道ID
#         channel_id = video_info["snippet"]["channelId"]
        
#         # 获取频道信息并保存到数据库
#         channel_info = get_channel_info(channel_id)
        
#         # 构建响应
#         snippet = channel_info["snippet"]
#         statistics = channel_info["statistics"]
        
#         return {
#             "channel_id": channel_id,
#             "title": snippet.get("title"),
#             "description": snippet.get("description"),
#             "custom_url": snippet.get("customUrl"),
#             "thumbnail_url": snippet.get("thumbnails", {}).get("high", {}).get("url"),
#             "subscriber_count": int(statistics.get("subscriberCount", 0)),
#             "video_count": int(statistics.get("videoCount", 0)),
#             "view_count": int(statistics.get("viewCount", 0)),
#             "published_at": snippet.get("publishedAt"),
#             "channel_url": f"https://www.youtube.com/channel/{channel_id}"
#         }
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"处理请求时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# @app.get(
#     "/api/channel",
#     response_model=ChannelResponse,
#     responses={404: {"model": ErrorResponse}, 400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
#     tags=["频道信息"],
#     summary="根据频道URL或ID获取频道信息",
#     description="根据提供的YouTube频道URL、频道ID或用户名(@username)，返回频道信息"
# )
# async def get_channel(
#     channel_url_or_id: str = Query(..., description="YouTube频道URL、ID或用户名")
# ):
#     try:
#         # 从频道URL、ID或用户名中提取频道ID
#         channel_id = extract_channel_id(channel_url_or_id)
        
#         # 获取频道信息并保存到数据库
#         channel_info = get_channel_info(channel_id)
        
#         # 构建响应
#         snippet = channel_info["snippet"]
#         statistics = channel_info["statistics"]
        
#         return {
#             "channel_id": channel_id,
#             "title": snippet.get("title"),
#             "description": snippet.get("description"),
#             "custom_url": snippet.get("customUrl"),
#             "thumbnail_url": snippet.get("thumbnails", {}).get("high", {}).get("url"),
#             "subscriber_count": int(statistics.get("subscriberCount", 0)),
#             "video_count": int(statistics.get("videoCount", 0)),
#             "view_count": int(statistics.get("viewCount", 0)),
#             "published_at": snippet.get("publishedAt"),
#             "channel_url": f"https://www.youtube.com/channel/{channel_id}"
#         }
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"处理请求时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# @app.post(
#     "/api/video/download",
#     tags=["视频下载"],
#     summary="下载YouTube视频",
#     description="提供YouTube视频URL，下载完整视频"
# )
# async def download_video(request: VideoDownloadRequest):
#     try:
#         # 创建唯一的下载目录
#         download_id = str(uuid.uuid4())
#         download_dir = os.path.join(TEMP_DIR, download_id)
#         os.makedirs(download_dir, exist_ok=True)
        
#         # 提取视频ID
#         video_id = extract_video_id_from_url(request.video_url)
        
#         # 设置下载选项
#         ydl_opts = {
#             'format': request.format,
#             'outtmpl': os.path.join(download_dir, '%(title)s.%(ext)s'),
#             'quiet': False,
#         }
        
#         # 下载视频
#         with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#             info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
#             downloaded_file = ydl.prepare_filename(info)
        
#         return {
#             "message": "视频下载成功",
#             "video_id": video_id,
#             "title": info.get('title'),
#             "download_path": downloaded_file,
#             "download_id": download_id
#         }
#     except Exception as e:
#         logger.error(f"下载视频失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"下载视频失败: {str(e)}")

# @app.post(
#     "/api/video/download/clip",
#     tags=["视频下载"],
#     summary="下载YouTube视频片段",
#     description="提供YouTube视频URL和时间范围，下载视频片段"
# )
# async def download_video_clip(request: VideoClipRequest):
#     try:
#         # 创建唯一的下载目录
#         download_id = str(uuid.uuid4())
#         download_dir = os.path.join(TEMP_DIR, download_id)
#         os.makedirs(download_dir, exist_ok=True)
        
#         # 提取视频ID
#         video_id = extract_video_id_from_url(request.video_url)
        
#         # 设置下载选项，包括时间范围
#         ydl_opts = {
#             'format': request.format,
#             'outtmpl': os.path.join(download_dir, '%(title)s_clip.%(ext)s'),
#             'quiet': False,
#             'download_ranges': download_range_func(None, [(request.start_time, request.end_time)]),
#             'force_keyframes_at_cuts': True,
#         }
        
#         # 下载视频片段
#         with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#             info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
#             downloaded_file = ydl.prepare_filename(info)
        
#         return {
#             "message": "视频片段下载成功",
#             "video_id": video_id,
#             "title": info.get('title'),
#             "clip_start": request.start_time,
#             "clip_end": request.end_time,
#             "download_path": downloaded_file,
#             "download_id": download_id
#         }
#     except Exception as e:
#         logger.error(f"下载视频片段失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"下载视频片段失败: {str(e)}")

# @app.post(
#     "/api/audio/download",
#     tags=["音频下载"],
#     summary="下载YouTube视频的音频",
#     description="提供YouTube视频URL，仅下载音频部分"
# )
# async def download_audio(request: AudioDownloadRequest):
#     try:
#         # 创建唯一的下载目录
#         download_id = str(uuid.uuid4())
#         download_dir = os.path.join(TEMP_DIR, download_id)
#         os.makedirs(download_dir, exist_ok=True)
        
#         # 提取视频ID
#         video_id = extract_video_id_from_url(request.video_url)
        
#         # 设置下载选项
#         ydl_opts = {
#             'format': request.format,
#             'outtmpl': os.path.join(download_dir, '%(title)s.%(ext)s'),
#             'quiet': False,
#         }
        
#         # 下载音频
#         with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#             info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=True)
#             downloaded_file = ydl.prepare_filename(info)
        
#         return {
#             "message": "音频下载成功",
#             "video_id": video_id,
#             "title": info.get('title'),
#             "download_path": downloaded_file,
#             "download_id": download_id
#         }
#     except Exception as e:
#         logger.error(f"下载音频失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"下载音频失败: {str(e)}")

# @app.post(
#     "/api/channel/videos/date-range",
#     tags=["频道视频"],
#     summary="获取频道指定日期范围内的视频",
#     description="提供YouTube频道URL和日期范围，抓取并保存视频信息到数据库"
# )
# async def get_channel_videos_by_date_range(request: ChannelVideosDateRangeRequest):
#     try:
#         # 从频道URL、ID或用户名中提取频道ID
#         channel_id = extract_channel_id(request.channel_url)
        
#         # 创建任务记录
#         task_params = {
#             "channel_id": channel_id, 
#             "start_date": request.start_date,
#             "end_date": request.end_date,
#             "fetch_type": "date_range_videos"
#         }
#         task_id = TaskDao.create_task("date_range_videos", task_params)
        
#         # 更新任务状态为运行中
#         TaskDao.update_task_status(task_id, "running")
        
#         # 获取频道信息
#         channel_info = get_channel_info(channel_id)
#         channel_title = channel_info["snippet"]["title"]
        
#         # 设置yt-dlp选项
#         ydl_opts = {
#             'quiet': True,
#             'extract_flat': True,  # 获取播放列表而不下载视频
#         }
        
#         channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
        
#         try:
#             # 获取频道所有视频
#             with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#                 info = ydl.extract_info(channel_url, download=False)
#                 videos = info.get('entries', [])
            
#             logger.info(f"从频道 {channel_title} 获取到 {len(videos)} 个视频")
            
#             # 创建DAO用于保存视频
#             from dao.youtube_dao import YouTubeDao
            
#             # 开始获取每个视频的详细信息并根据日期过滤
#             filtered_videos = []
#             processed_count = 0
#             saved_count = 0
#             updated_count = 0
            
#             # 转换日期字符串为datetime对象，用于比较
#             start_date = datetime.strptime(request.start_date, "%Y%m%d")
#             end_date = datetime.strptime(request.end_date, "%Y%m%d")
            
#             for video_entry in videos:
#                 if processed_count >= request.max_videos:
#                     break
                
#                 video_id = video_entry['id']
#                 processed_count += 1
                
#                 # 获取视频详细信息
#                 video_opts = {
#                     'quiet': True,
#                     'skip_download': True,  # 不下载视频
#                     'player_client': 'tv,web,tv_embedded,web_creator', # 解决年龄问题
#                 }
                
#                 try:
#                     with yt_dlp.YoutubeDL(video_opts) as ydl:
#                         video_info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                        
#                     # 提取上传日期
#                     upload_date_str = video_info.get('upload_date')
#                     if not upload_date_str:
#                         continue
                    
#                     # 转换上传日期为datetime对象
#                     upload_date = datetime.strptime(upload_date_str, "%Y%m%d")
                    
#                     # 检查是否在日期范围内
#                     if start_date <= upload_date <= end_date:
#                         # 构建保存到数据库的视频信息
#                         video_data = {
#                             'id': video_id,
#                             'channelId': video_info.get('channel_id'),
#                             'title': video_info.get('title', ''),
#                             'description': video_info.get('description', ''),
#                             'publishedAt': upload_date.strftime("%Y-%m-%d %H:%M:%S"),
#                             'thumbnailUrl': video_info.get('thumbnail', ''),
#                             'viewCount': int(video_info.get('view_count', 0) or 0),
#                             'likeCount': int(video_info.get('like_count', 0) or 0),
#                             'commentCount': int(video_info.get('comment_count', 0) or 0),
#                             'duration': format_duration(video_info.get('duration', 0)),  # 转换duration为INTERVAL格式
#                             'tags': video_info.get('tags', []),
#                             'categories': video_info.get('categories', []),
#                             'uploadDate': video_info.get('upload_date'),
#                             'channel': video_info.get('channel', ''),
#                             'timestamp': video_info.get('timestamp'),
#                             'thumbnails': video_info.get('thumbnails', []),
#                             'raw_response': video_info
#                         }
                        
#                         # 检查视频是否已存在
#                         existing_video = YouTubeDao.get_video_by_youtube_id(str(video_id))
                        
#                         if existing_video and len(existing_video) > 0:
#                             # 检查数据是否有变化
#                             if (existing_video[0][1] != video_data['viewCount'] or 
#                                 existing_video[0][2] != video_data['likeCount'] or 
#                                 existing_video[0][3] != video_data['commentCount']):
                                
#                                 # 更新视频数据
#                                 YouTubeDao.update_video(video_data)
#                                 updated_count += 1
#                                 logger.info(f"已更新视频: {video_data['title']}, 观看次数从 {existing_video[0][1]} 更新为 {video_data['viewCount']}")
#                             else:
#                                 logger.info(f"视频数据无变化，跳过更新: {video_data['title']}")
#                         else:
#                             # 保存新视频到数据库
#                             if YouTubeDao.save_video(video_data):
#                                 saved_count += 1
#                                 logger.info(f"已保存新视频: {video_data['title']}")
#                             else:
#                                 logger.error(f"保存视频失败: {video_data['title']}")
                        
#                         filtered_videos.append(video_data)
                        
#                 except Exception as e:
#                     error_msg = str(e)
#                     # 会员专属视频，记录错误并跳过
#                     if "members-only content" in error_msg or "Join this channel" in error_msg:
#                         logger.warning(f"跳过会员专属视频: {video_id}, 原因: {error_msg}")
#                     else:
#                         logger.error(f"处理视频 {video_id} 失败: {error_msg}")
#                     continue
            
#             # 更新任务状态为成功
#             TaskDao.update_task_status(task_id, "success", result={
#                 "processed_count": processed_count,
#                 "saved_count": saved_count,
#                 "updated_count": updated_count,
#                 "channel_title": channel_title
#             })
            
#             return {
#                 "message": f"成功获取频道 {channel_title} 在指定日期范围内的视频",
#                 "channel_id": channel_id,
#                 "channel_title": channel_title,
#                 "date_range": f"{request.start_date} 至 {request.end_date}",
#                 "videos_found": len(filtered_videos),
#                 "videos_saved": saved_count,
#                 "videos_updated": updated_count,
#                 "videos_processed": processed_count,
#                 "task_id": task_id
#             }
            
#         except Exception as e:
#             error_msg = f"获取频道视频失败: {str(e)}"
#             logger.error(error_msg)
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#             raise HTTPException(status_code=500, detail=error_msg)
        
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"处理请求时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# # 创建DAO用于保存视频
# from dao.youtube_dao import YouTubeDao

# # 后台处理函数
# def process_channel_videos_background(task_id: str, channel_id: str, channel_url: str, max_videos: int):
#     """在后台处理频道视频获取和保存的任务"""
#     try:
#         # 获取任务开始时间
#         start_time = time.time()
        
#         # 更新任务状态，记录开始时间
#         try:
#             TaskDao.update_task_status(task_id, "running", result={"started_at": start_time})
#         except Exception as e:
#             logger.warning(f"Task {task_id}: 更新任务开始时间失败: {str(e)}")
        
#         # 获取频道信息
#         try:
#             channel_info_data = get_channel_info(channel_id)
#             channel_title = channel_info_data["snippet"]["title"]
#         except HTTPException as http_exc:
#             error_msg = f"获取频道信息失败: {http_exc.detail}"
#             logger.error(f"Task {task_id}: {error_msg}")
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#             return
#         except Exception as e:
#             error_msg = f"获取频道信息时发生意外错误: {str(e)}"
#             logger.error(f"Task {task_id}: {error_msg}")
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#             return
            
#         # 设置yt-dlp选项
#         ydl_opts = get_yt_dlp_opts({
#             'extract_flat': True,  # 获取播放列表而不下载视频
#         })
        
#         # 使用 yt-dlp 获取视频列表
#         try:
#             with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#                 # 确保使用有效的 URL
#                 if not channel_url or '{channel_id}' in channel_url:
#                     channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
                
#                 info = ydl.extract_info(channel_url, download=False)
#                 videos = info.get('entries', [])
#             logger.info(f"Task {task_id}: 从频道 {channel_title} 获取到 {len(videos)} 个视频列表")
#         except Exception as e:
#             error_msg = f"使用yt-dlp获取视频列表失败: {str(e)}"
#             logger.error(f"Task {task_id}: {error_msg}")
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#             return

#         # 开始获取每个视频的详细信息
#         processed_count = 0
#         saved_count = 0
#         skipped_count = 0
#         last_status_update = time.time()

#         # 设置视频获取选项
#         video_opts = get_yt_dlp_opts({
#             'skip_download': True,  # 不下载视频
#         })
        
#         # 批量处理视频 - 每批次最多处理的视频数量
#         batch_size = 10
#         batch_videos = []
        
#         for video_entry in videos:
#             video_id = video_entry['id']
            
#             # 检查视频是否已存在 (get_video_by_youtube_id 返回的是列表或None)
#             existing_video = YouTubeDao.get_video_by_youtube_id(str(video_id))
#             if existing_video:
#                 logger.info(f"Task {task_id}: 视频已存在于数据库: {video_id}, 跳过。")
#                 skipped_count += 1
#                 continue

#             processed_count += 1
            
#             try:
#                 # 添加随机延迟，但使用线程安全的方式 - 这是在线程中执行的代码
#                 delay = random.uniform(1, 3)
#                 time.sleep(delay)  # 在线程中使用 time.sleep 是安全的，因为它不会阻塞事件循环
                
#                 with yt_dlp.YoutubeDL(video_opts) as ydl:
#                     video_info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                
#                 # 构建保存到数据库的视频信息
#                 video_data = {
#                     'id': video_id,
#                     'channelId': video_info.get('channel_id'),
#                     'title': video_info.get('title', ''),
#                     'description': video_info.get('description', ''),
#                     'publishedAt': video_info.get('upload_date', None),
#                     'thumbnailUrl': video_info.get('thumbnail', ''),
#                     'viewCount': int(video_info.get('view_count', 0) or 0),
#                     'likeCount': int(video_info.get('like_count', 0) or 0),
#                     'commentCount': int(video_info.get('comment_count', 0) or 0),
#                     'duration': format_duration(video_info.get('duration', 0)),
#                     'tags': video_info.get('tags', []),
#                     'categories': video_info.get('categories', []),
#                     'uploadDate': video_info.get('upload_date'),
#                     'channel': video_info.get('channel', ''),
#                     'timestamp': video_info.get('timestamp'),
#                     'thumbnails': video_info.get('thumbnails', []),
#                     'raw_response': video_info
#                 }

#                 # publishedAt 格式转换
#                 if video_data['publishedAt']:
#                     from datetime import datetime
#                     try:
#                         dt_object = datetime.strptime(video_data['publishedAt'], '%Y%m%d')
#                         video_data['publishedAt'] = dt_object.strftime('%Y-%m-%d %H:%M:%S') 
#                     except ValueError:
#                         logger.warning(f"Task {task_id}: 视频 {video_id} 的日期格式无效: {video_data['publishedAt']}")
#                         video_data['publishedAt'] = None

#                 # 添加到批次处理列表
#                 batch_videos.append(video_data)
                
#                 # 达到批次大小或最后一个视频时，进行批量保存
#                 if len(batch_videos) >= batch_size or video_id == videos[-1]['id']:
#                     try:
#                         # 实现批量保存逻辑（如果 YouTubeDao 支持）
#                         for video in batch_videos:
#                             # 目前仍使用单个视频保存
#                             if YouTubeDao.save_video(video):
#                                 saved_count += 1
#                                 logger.info(f"Task {task_id}: 已保存视频: {video['title']} (ID: {video['id']})")
#                             else:
#                                 logger.error(f"Task {task_id}: 保存视频失败: {video['title']} (ID: {video['id']})")
#                     except Exception as batch_error:
#                         logger.error(f"Task {task_id}: 批量处理视频时出错: {str(batch_error)}")
                    
#                     # 清空批次
#                     batch_videos = []
                
#                 # 定期更新任务状态（每分钟更新一次）
#                 current_time = time.time()
#                 if current_time - last_status_update >= 60:
#                     progress = {
#                         "processed": processed_count,
#                         "saved": saved_count,
#                         "skipped": skipped_count,
#                         "total_videos": len(videos),
#                         "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#                     }
#                     TaskDao.update_task_status(task_id, "running", result=progress)
#                     last_status_update = current_time
                
#             except Exception as e:
#                 error_msg = str(e)
#                 if "members-only content" in error_msg or "Join this channel" in error_msg:
#                     logger.warning(f"Task {task_id}: 跳过会员专属视频: {video_id}, 原因: {error_msg}")
#                 elif "Private video" in error_msg:
#                      logger.warning(f"Task {task_id}: 跳过私密视频: {video_id}, 原因: {error_msg}")
#                 elif "Video unavailable" in error_msg:
#                      logger.warning(f"Task {task_id}: 跳过不可用视频: {video_id}, 原因: {error_msg}")
#                 else:
#                     logger.error(f"Task {task_id}: 处理视频 {video_id} 失败: {error_msg}")
#                 continue
        
#         # 计算总处理时间
#         processing_time = time.time() - start_time
#         processing_minutes = round(processing_time / 60, 2)
        
#         # 所有视频处理完毕，更新任务状态为成功
#         TaskDao.update_task_status(task_id, "success", result={
#             "processed_count": processed_count,
#             "saved_count": saved_count,
#             "skipped_count": skipped_count,
#             "total_videos_listed": len(videos),
#             "channel_title": channel_title,
#             "processing_time_minutes": processing_minutes,
#             "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#         })
#         logger.info(f"Task {task_id}: 频道 {channel_title} 视频处理完成。用时: {processing_minutes}分钟, 处理: {processed_count}, 保存: {saved_count}, 跳过: {skipped_count}, 列表总数: {len(videos)}")

#     except Exception as e:
#         # 捕获后台任务中的顶层异常
#         error_msg = f"后台任务执行失败: {str(e)}"
#         logger.error(f"Task {task_id}: {error_msg}", exc_info=True)
#         try:
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#         except Exception as db_error:
#             logger.error(f"Task {task_id}: 更新任务状态为失败时也发生错误: {str(db_error)}")

# async def task_scheduler():
#     """
#     后台任务调度器，定期检查并启动 pending 状态的任务。
#     保证同一时间只有一个频道视频获取任务在运行。
#     """
#     logger.info("任务调度器已启动，开始检查待处理任务...")
#     check_interval = 60 # 检查间隔改为3分钟，之前是1200秒（20分钟）
#     processing_check_interval = 10 # 处理完任务后检查间隔，10秒
#     task_timeout = 300 # 任务超时时间，5分钟
    
#     while True:
#         try:
#             # 检查是否有任务在运行
#             running_task = TaskDao.get_running_task()
#             if running_task:
#                 # 检查任务是否超时
#                 last_updated = running_task.get('updated_at')
#                 if last_updated:
#                     from datetime import datetime
#                     try:
#                         last_updated_time = datetime.strptime(last_updated, '%Y-%m-%d %H:%M:%S')
#                         time_diff = (datetime.now() - last_updated_time).total_seconds()
                        
#                         if time_diff > task_timeout:
#                             # 任务超时，标记为失败
#                             error_msg = f"任务 {running_task['id']} 已超时（{time_diff}秒未更新），可能由于服务中断导致"
#                             logger.warning(error_msg)
#                             TaskDao.update_task_status(running_task['id'], "failed", error_message=error_msg)
#                             await asyncio.sleep(processing_check_interval)
#                             continue
#                     except Exception as e:
#                         logger.error(f"检查任务 {running_task['id']} 超时状态时出错: {str(e)}")
                
#                 logger.debug(f"调度器检测到任务 {running_task['id']} 正在运行，等待 {check_interval} 秒")
#                 await asyncio.sleep(check_interval)
#                 continue

#             # 没有任务在运行，检查是否有待处理任务
#             pending_task = TaskDao.get_next_pending_task()
#             if pending_task:
#                 task_id = pending_task['id']
#                 # 确保从数据库结果中正确获取 channel_id
#                 channel_id = pending_task.get('channel_id')
                
#                 if not channel_id:
#                     logger.error(f"任务 {task_id} 缺少 channel_id，将标记为失败")
#                     TaskDao.update_task_status(task_id, "failed", error_message="任务缺少 channel_id 参数")
#                     await asyncio.sleep(processing_check_interval)
#                     continue
                
#                 # 正确构建 channel_url，修复之前的模板字符串错误
#                 channel_url = f"https://www.youtube.com/channel/{channel_id}"
                
#                 # 从参数中提取 max_videos，或使用默认值
#                 max_videos = 50 # 默认值
                
#                 # 尝试从任务参数中获取更详细的配置
#                 try:
#                     # 这里要确保 TaskDao.get_next_pending_task() 方法改进后返回的是完整任务信息，
#                     # 包括完整的 parameters/params 字段
#                     if hasattr(pending_task, 'parameters'):
#                         params = pending_task.get('parameters', {})
#                     elif hasattr(pending_task, 'params'):
#                         params = pending_task.get('params', {})
#                     else:
#                         # 如果无法从返回结果中获取参数，则尝试重新查询任务详情
#                         params = {}
#                         logger.warning(f"任务 {task_id} 缺少参数信息，使用默认配置")
                        
#                     # 如果找到参数，则从中提取需要的值
#                     if params:
#                         # 可能的参数字段名
#                         channel_url_candidates = ['channel_url', 'channelUrl', 'url']
#                         for field in channel_url_candidates:
#                             if field in params and params[field]:
#                                 channel_url = params[field]
#                                 break
                                
#                         # 提取 max_videos
#                         if 'max_videos' in params and params['max_videos']:
#                             max_videos = int(params['max_videos'])
#                 except Exception as param_err:
#                     logger.error(f"解析任务 {task_id} 参数时出错: {str(param_err)}")

#                 logger.info(f"调度器发现待处理任务 {task_id} (Channel: {channel_id})，准备启动...")
#                 TaskDao.update_task_status(task_id, "running")
#                 logger.info(f"任务 {task_id} 状态已更新为 running")

#                 # 在线程池中执行阻塞的后台处理函数
#                 loop = asyncio.get_running_loop()
#                 try:
#                     logger.info(f"开始在线程池中执行任务 {task_id} 的处理函数...")
#                     await loop.run_in_executor(
#                         None, # 使用默认线程池
#                         process_channel_videos_background,
#                         task_id,
#                         channel_id,
#                         channel_url,
#                         max_videos
#                     )
#                     logger.info(f"任务 {task_id} 的处理函数已在线程池中执行完毕。")

#                 except Exception as exec_error:
#                     error_msg = f"执行任务 {task_id} 的后台函数时捕获到未处理异常: {str(exec_error)}"
#                     logger.error(error_msg, exc_info=True)
#                     try:
#                         TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#                     except Exception as db_err:
#                         logger.error(f"尝试将失败任务 {task_id} 标记为失败时出错: {db_err}")

#                 # 任务执行完毕后，等待 10 秒再继续检查
#                 await asyncio.sleep(processing_check_interval)

#             else:
#                 # 没有待处理任务
#                 logger.debug(f"调度器未发现待处理任务，等待 {check_interval} 秒")
#                 await asyncio.sleep(check_interval)

#         except Exception as e:
#             logger.error(f"任务调度器主循环发生错误: {str(e)}", exc_info=True)
#             # 异常时等待更长时间，防止频繁错误消耗资源
#             await asyncio.sleep(check_interval)

# @app.post(
#     "/api/channel/videos/history",
#     tags=["频道视频"],
#     summary="提交获取频道所有视频的任务",
#     description="提供YouTube频道URL，提交一个后台任务来获取并保存频道下的所有视频信息到数据库"
# )
# async def submit_channel_videos_task(request: ChannelVideosRequest):
#     try:
#         # 从频道URL、ID或用户名中提取频道ID
#         channel_id = extract_channel_id(request.channel_url)
#         if channel_id is None:
#             channel_id = get_channel_id_from_username(request.channel_url)
#             if channel_id is None:
#                 raise ValueError("无法从提供的URL或ID中提取频道ID")

#         # 检查是否已有相同 channel_id 且状态为 pending/running 的任务
#         running_task = TaskDao.get_running_task()
#         if running_task and running_task.get('channel_id') == channel_id:
#             return {
#                 "message": f"该频道 {channel_id} 的视频获取任务正在处理中",
#                 "task_id": running_task['id'],
#                 "status": "running",
#                 "channel_id": channel_id
#             }

#         # 正确构建和保存 channel_url
#         channel_url = f"https://www.youtube.com/channel/{channel_id}"
        
#         # 创建 pending 任务，让调度器负责启动
#         task_params = {
#             "channel_id": channel_id,
#             "fetch_type": "channel_videos",
#             "channel_url": channel_url,
#             "max_videos": request.max_videos
#         }
        
#         task_id = TaskDao.create_task("channel_videos", task_params)
#         logger.info(f"为频道 {channel_id} 创建了新的待处理任务 {task_id}")

#         # 返回成功提交的信息
#         return {
#             "message": "已成功提交频道历史视频获取任务，任务已加入队列等待处理",
#             "channel_id": channel_id,
#             "task_id": task_id,
#             "status": "pending"
#         }

#     except ValueError as e:
#         logger.error(f"提取频道ID失败: {str(e)}")
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         error_msg = f"提交频道视频获取任务时发生错误: {str(e)}"
#         logger.error(error_msg, exc_info=True)
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {error_msg}")

# @app.post(
#     "/api/channel/videos/update",
#     tags=["频道视频"],
#     summary="更新频道下的所有视频",
#     description="提供YouTube频道URL，获取并保存频道下的所有视频信息到数据库"
# )
# async def update_channel_videos(request: ChannelVideosRequest):
#     try:
#         # 从频道URL、ID或用户名中提取频道ID
#         channel_id = extract_channel_id(request.channel_url)
        
#         # 创建任务记录
#         task_params = {"channel_id": channel_id, "fetch_type": "channel_videos"}
#         task_id = TaskDao.create_task("channel_videos", task_params)
        
#         # 更新任务状态为运行中
#         TaskDao.update_task_status(task_id, "running")
        
#         # 获取频道信息
#         channel_info = get_channel_info(channel_id)
#         channel_title = channel_info["snippet"]["title"]
        
#         # 设置yt-dlp选项
#         ydl_opts = get_yt_dlp_opts({
#             'extract_flat': True,  # 获取播放列表而不下载视频
#         })
        
#         channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
        
#         try:
#             # 获取频道所有视频
#             with yt_dlp.YoutubeDL(ydl_opts) as ydl:
#                 info = ydl.extract_info(channel_url, download=False)
#                 videos = info.get('entries', [])
            
#             logger.info(f"从频道 {channel_title} 获取到 {len(videos)} 个视频")
            
#             # 创建DAO用于保存视频
#             from dao.youtube_dao import YouTubeDao
            
#             # 开始获取每个视频的详细信息
#             processed_count = 0
#             saved_count = 0
#             updated_count = 0
            
#             # 设置视频获取选项
#             video_opts = get_yt_dlp_opts({
#                 'skip_download': True,  # 不下载视频
#             })
            
#             for video_entry in videos:
#                 # 如果视频数量大于50，则停止
#                 if processed_count >= request.max_videos:
#                     break
                
#                 video_id = video_entry['id']
#                 processed_count += 1
                
#                 try:
#                     # 添加随机延迟，避免请求过于频繁
#                     time.sleep(random.uniform(1, 3))
                    
#                     with yt_dlp.YoutubeDL(video_opts) as ydl:
#                         video_info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                    
#                     # 构建保存到数据库的视频信息
#                     video_data = {
#                         'id': video_id,
#                         'channelId': video_info.get('channel_id'),
#                         'title': video_info.get('title', ''),
#                         'description': video_info.get('description', ''),
#                         'publishedAt': video_info.get('upload_date', ''),
#                         'thumbnailUrl': video_info.get('thumbnail', ''),
#                         'viewCount': int(video_info.get('view_count', 0) or 0),
#                         'likeCount': int(video_info.get('like_count', 0) or 0),
#                         'commentCount': int(video_info.get('comment_count', 0) or 0),
#                         'duration': format_duration(video_info.get('duration', 0)),
#                         'tags': video_info.get('tags', []),
#                         'categories': video_info.get('categories', []),
#                         'uploadDate': video_info.get('upload_date'),
#                         'channel': video_info.get('channel', ''),
#                         'timestamp': video_info.get('timestamp'),
#                         'thumbnails': video_info.get('thumbnails', []),
#                         'raw_response': video_info
#                     }
                    
#                     # 检查视频是否已存在
#                     existing_video = YouTubeDao.get_video_by_youtube_id(str(video_id))
                    
#                     if existing_video and len(existing_video) > 0:
#                         # 检查数据是否有变化
#                         if (existing_video[0][1] != video_data['viewCount'] or 
#                             existing_video[0][2] != video_data['likeCount'] or 
#                             existing_video[0][3] != video_data['commentCount']):
                            
#                             # 更新视频数据
#                             YouTubeDao.update_video(video_data)
#                             updated_count += 1
#                             logger.info(f"已更新视频: {video_data['title']}, 观看次数从 {existing_video[0][1]} 更新为 {video_data['viewCount']}")
#                         else:
#                             logger.info(f"视频数据无变化，跳过更新: {video_data['title']}")
#                     else:
#                         # 保存新视频到数据库
#                         if YouTubeDao.save_video(video_data):
#                             saved_count += 1
#                             logger.info(f"已保存新视频: {video_data['title']}")
#                         else:
#                             logger.error(f"保存视频失败: {video_data['title']}")
                    
#                 except Exception as e:
#                     error_msg = str(e)
#                     # 会员专属视频，记录警告并跳过
#                     if "members-only content" in error_msg or "Join this channel" in error_msg:
#                         logger.warning(f"跳过会员专属视频: {video_id}, 原因: {error_msg}")
#                     else:
#                         logger.error(f"处理视频 {video_id} 失败: {error_msg}")
#                     continue
            
#             # 更新任务状态为成功
#             TaskDao.update_task_status(task_id, "success", result={
#                 "processed_count": processed_count,
#                 "saved_count": saved_count,
#                 "updated_count": updated_count,
#                 "channel_title": channel_title
#             })
            
#             return {
#                 "message": f"成功获取频道 {channel_title} 的视频",
#                 "channel_id": channel_id,
#                 "channel_title": channel_title,
#                 "videos_found": len(videos),
#                 "videos_saved": saved_count,
#                 "videos_updated": updated_count,
#                 "videos_processed": processed_count,
#                 "task_id": task_id
#             }
            
#         except Exception as e:
#             error_msg = f"获取频道视频失败: {str(e)}"
#             logger.error(error_msg)
#             TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
#             raise HTTPException(status_code=500, detail=error_msg)
        
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"处理请求时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# # 添加任务状态查询接口
# @app.get(
#     "/api/tasks/{task_id}",
#     tags=["任务管理"],
#     summary="查询任务状态",
#     description="根据任务ID查询任务的当前状态、进度和结果"
# )
# async def get_task_status(task_id: str):
#     try:
#         # 假设 TaskDao 有 get_task_by_id 方法，返回任务信息
#         task_info = TaskDao.get_task_by_id(task_id)
        
#         if not task_info:
#             raise HTTPException(status_code=404, detail=f"未找到任务: {task_id}")
        
#         # 构建响应
#         response = {
#             "task_id": task_id,
#             "status": task_info.get("status", "unknown"),
#             "task_type": task_info.get("task_type", ""),
#             "created_at": task_info.get("created_at", ""),
#             "updated_at": task_info.get("updated_at", ""),
#         }
        
#         # 添加结果或错误信息（如果有）
#         if task_info.get("result"):
#             response["result"] = task_info["result"]
#         if task_info.get("error_message"):
#             response["error"] = task_info["error_message"]
            
#         return response
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"查询任务状态时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# # 清理下载文件的定时任务（可选）
# @app.on_event("startup")
# async def setup_cleanup_task():
#     import asyncio
#     from datetime import datetime, timedelta
    
#     async def cleanup_old_downloads():
#         while True:
#             try:
#                 now = datetime.now()
#                 cutoff = now - timedelta(hours=1)  # 删除1小时前的文件
                
#                 for item in os.listdir(TEMP_DIR):
#                     item_path = os.path.join(TEMP_DIR, item)
#                     if os.path.isdir(item_path):
#                         created_time = datetime.fromtimestamp(os.path.getctime(item_path))
#                         if created_time < cutoff:
#                             shutil.rmtree(item_path)
#                             logger.info(f"已清理过期下载目录: {item_path}")
#             except Exception as e:
#                 logger.error(f"清理下载目录失败: {str(e)}")
            
#             await asyncio.sleep(3600)  # 每小时运行一次
    
#     asyncio.create_task(cleanup_old_downloads())

# # 启动事件
# @app.on_event("startup")
# async def startup_event():
#     # 设置日志
#     setup_logging_from_config()
    
#     # 初始化数据库
#     setup_database()
    
#     # 确保统计数据表结构已创建
#     ChannelStatsUpdater.ensure_database_structure()
    
#     # 启动统计数据每日更新任务
#     asyncio.create_task(schedule_daily_statistics_update())
    
#     # 启动任务调度器
#     asyncio.create_task(task_scheduler())
    
#     logger.info("FastAPI应用已启动，并已启动后台任务调度器")

# # 如果直接运行此文件
# if __name__ == "__main__":
#     uvicorn.run("fastapi_app:app", host="0.0.0.0", port=8100, reload=True) 

# def format_duration(seconds):
#     """
#     将秒数转换为PostgreSQL的INTERVAL格式
#     例如：3600 -> '1 hour', 3661 -> '1 hour 1 minute 1 second'
#     """
#     if not seconds:
#         return None
        
#     hours = seconds // 3600
#     minutes = (seconds % 3600) // 60
#     seconds = seconds % 60
    
#     parts = []
#     if hours > 0:
#         parts.append(f"{hours} hour{'s' if hours > 1 else ''}")
#     if minutes > 0:
#         parts.append(f"{minutes} minute{'s' if minutes > 1 else ''}")
#     if seconds > 0:
#         parts.append(f"{seconds} second{'s' if seconds > 1 else ''}")
    
#     return ' '.join(parts) if parts else None 

# async def schedule_daily_statistics_update():
#     """
#     调度频道统计数据每日更新任务
#     每天凌晨 2:00 运行，处理前一天的数据
#     """
#     while True:
#         now = datetime.now()
        
#         # 计算到下一个凌晨 2:00 的秒数
#         target_time = now.replace(hour=2, minute=0, second=0, microsecond=0)
#         if now.hour >= 2:
#             target_time = target_time + timedelta(days=1)
        
#         # 计算需要等待的秒数
#         seconds_until_target = (target_time - now).total_seconds()
        
#         logger.info(f"频道统计数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
#                   f"等待 {seconds_until_target:.2f} 秒")
        
#         # 等待到目标时间
#         await asyncio.sleep(seconds_until_target)
        
#         # 运行统计数据更新
#         try:
#             logger.info("开始执行频道统计数据每日更新")
#             # 处理前一天的数据
#             target_date = datetime.now() - timedelta(days=1)
#             success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
#             logger.info(f"频道统计数据更新完成: {'成功' if success else '失败'}")
#         except Exception as e:
#             logger.error(f"频道统计数据更新出错: {str(e)}")
        
#         # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
#         await asyncio.sleep(3600) 

# class StatisticsUpdateRequest(BaseModel):
#     date: Optional[str] = Field(None, description="要处理的日期 (YYYY-MM-DD格式)，默认为昨天")
#     channel_id: Optional[str] = Field(None, description="只处理指定频道ID的数据，默认处理所有频道")

# @app.post(
#     "/api/statistics/update",
#     tags=["数据统计"],
#     summary="手动更新频道统计数据",
#     description="手动触发频道统计数据的计算和更新"
# )
# async def update_channel_statistics(request: StatisticsUpdateRequest):
#     """手动触发频道统计数据更新"""
#     try:
#         target_date = None
#         if request.date:
#             try:
#                 target_date = datetime.strptime(request.date, '%Y-%m-%d')
#             except ValueError:
#                 return {"success": False, "error": f"日期格式无效: {request.date}，应为YYYY-MM-DD格式"}
        
#         # 处理单个频道或所有频道
#         if request.channel_id:
#             logger.info(f"手动触发频道 {request.channel_id} 的统计数据更新")
#             success = ChannelStatsUpdater.update_channel_statistics(request.channel_id, target_date)
#         else:
#             logger.info("手动触发所有频道的统计数据更新")
#             success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
        
#         return {
#             "success": success,
#             "message": "统计数据更新成功" if success else "统计数据更新失败，请查看日志获取详细信息"
#         }
#     except Exception as e:
#         logger.error(f"手动触发统计数据更新出错: {str(e)}")
#         return {"success": False, "error": str(e)} 