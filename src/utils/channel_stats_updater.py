#!/usr/bin/env python3
"""
频道时间统计更新工具

基于快照数据计算不同时间段(日/周/月/季/年)的频道统计数据，
包括订阅数、观看数、收益等指标的变化和增长率
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any, Optional, Tuple

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 修改导入路径
from src.database import get_db_pool
from src.utils.logger import get_logger

# 初始化日志记录器
logger = get_logger("channel_stats_updater")
db_pool = get_db_pool()

class ChannelStatsUpdater:
    """频道时间统计数据更新器"""
    
    @staticmethod
    def get_channels() -> List[Dict[str, Any]]:
        """获取所有频道列表
        
        Returns:
            List[Dict[str, Any]]: 频道列表
        """
        conn = None
        cursor = None
        try:
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            sql = """
                SELECT id, channel_id, title, subscriber_count, view_count 
                FROM ytb_channels 
                WHERE deleted_at IS NULL
                ORDER BY subscriber_count DESC
            """
            
            cursor.execute(sql)
            return cursor.fetchall()
            
        except Exception as e:
            logger.error(f"获取频道列表失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                db_pool.close_connection(conn, cursor)
    
    @staticmethod
    def get_channel_snapshots(channel_id: str, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取特定频道在给定日期范围内的快照数据
        
        Args:
            channel_id: 频道ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[dict]: 快照数据列表
        """
        conn = None
        cursor = None
        try:
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 现在channel_id是VARCHAR类型，可以直接查询，不需要通过内部ID
            query = """
            SELECT * FROM ytb_channel_stats_snapshots
            WHERE channel_id = %s
            AND snapshot_at BETWEEN %s AND %s
            AND deleted_at IS NULL
            ORDER BY snapshot_at ASC
            """
            
            cursor.execute(query, (channel_id, start_date, end_date))
            snapshots = cursor.fetchall()
            return snapshots
        except Exception as e:
            logger.error(f"获取频道快照失败: {str(e)}")
            return []
        finally:
            if conn and cursor:
                db_pool.close_connection(conn, cursor)
    
    @staticmethod
    def calculate_growth_rate(start_value: float, end_value: float) -> float:
        """计算增长率
        
        Args:
            start_value (float): 开始值
            end_value (float): 结束值
            
        Returns:
            float: 增长率(百分比)
        """
        if start_value == 0:
            return 100.0 if end_value > 0 else 0.0
        
        return ((end_value - start_value) / start_value) * 100
    
    @staticmethod
    def save_period_statistics(
        channel_id: str,
        period_type: str,
        period_start: datetime,
        period_end: datetime,
        start_subscribers: int,
        end_subscribers: int,
        start_views: int,
        end_views: int,
        start_revenue: float = 0,
        end_revenue: float = 0,
        year: int = None,
        quarter: int = None,
        month: int = None,
        week: int = None,
        day: int = None
    ) -> bool:
        """保存时间段统计数据
        
        Args:
            channel_id (str): 频道ID
            period_type (str): 周期类型(day/week/month/quarter/year)
            period_start (datetime): 周期开始日期
            period_end (datetime): 周期结束日期
            start_subscribers (int): 开始订阅数
            end_subscribers (int): 结束订阅数
            start_views (int): 开始观看数
            end_views (int): 结束观看数
            start_revenue (float): 开始收益
            end_revenue (float): 结束收益
            year (int): 年份
            quarter (int): 季度
            month (int): 月份
            week (int): 周数
            day (int): 日期
            
        Returns:
            bool: 操作是否成功
        """
        # 计算变化和增长率
        subscriber_change = end_subscribers - start_subscribers
        subscriber_growth_rate = ChannelStatsUpdater.calculate_growth_rate(start_subscribers, end_subscribers)
        
        view_change = end_views - start_views
        view_growth_rate = ChannelStatsUpdater.calculate_growth_rate(start_views, end_views)
        
        revenue_change = end_revenue - start_revenue
        revenue_growth_rate = ChannelStatsUpdater.calculate_growth_rate(start_revenue, end_revenue)
        
        conn = None
        cursor = None
        try:
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 检查表结构中是否有唯一约束
            check_constraint_sql = """
            SELECT con.conname AS constraint_name
            FROM pg_constraint con
            JOIN pg_class rel ON rel.oid = con.conrelid
            JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
            WHERE rel.relname = 'ytb_channel_time_statistics'
            AND con.contype = 'u'
            AND con.conname LIKE '%unique%'
            """
            
            cursor.execute(check_constraint_sql)
            constraint_exists = cursor.fetchone() is not None
            
            if constraint_exists:
                # 使用ON CONFLICT更新
                sql = """
                    INSERT INTO ytb_channel_time_statistics (
                        channel_id, period_type, period_start, period_end,
                        start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
                        start_views, end_views, view_change, view_growth_rate,
                        start_revenue, end_revenue, revenue_change, revenue_growth_rate,
                        year, quarter, month, week, day,
                        created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        NOW(), NOW()
                    ) ON CONFLICT (channel_id, period_type, period_start, period_end) WHERE deleted_at IS NULL
                    DO UPDATE SET
                        start_subscribers = EXCLUDED.start_subscribers,
                        end_subscribers = EXCLUDED.end_subscribers,
                        subscriber_change = EXCLUDED.subscriber_change,
                        subscriber_growth_rate = EXCLUDED.subscriber_growth_rate,
                        start_views = EXCLUDED.start_views,
                        end_views = EXCLUDED.end_views,
                        view_change = EXCLUDED.view_change,
                        view_growth_rate = EXCLUDED.view_growth_rate,
                        start_revenue = EXCLUDED.start_revenue,
                        end_revenue = EXCLUDED.end_revenue,
                        revenue_change = EXCLUDED.revenue_change,
                        revenue_growth_rate = EXCLUDED.revenue_growth_rate,
                        updated_at = NOW()
                    RETURNING id
                """
            else:
                # 不使用ON CONFLICT，而是先尝试更新，如果没有记录则插入
                check_sql = """
                    SELECT id FROM ytb_channel_time_statistics
                    WHERE channel_id = %s AND period_type = %s 
                    AND period_start = %s AND period_end = %s
                    AND deleted_at IS NULL
                """
                
                cursor.execute(check_sql, (
                    channel_id,
                    period_type,
                    period_start.date(),
                    period_end.date()
                ))
                
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 更新现有记录
                    sql = """
                        UPDATE ytb_channel_time_statistics SET
                            start_subscribers = %s,
                            end_subscribers = %s,
                            subscriber_change = %s,
                            subscriber_growth_rate = %s,
                            start_views = %s,
                            end_views = %s,
                            view_change = %s,
                            view_growth_rate = %s,
                            start_revenue = %s,
                            end_revenue = %s,
                            revenue_change = %s,
                            revenue_growth_rate = %s,
                            updated_at = NOW()
                        WHERE id = %s
                        RETURNING id
                    """
                    
                    cursor.execute(sql, (
                        start_subscribers,
                        end_subscribers,
                        subscriber_change,
                        subscriber_growth_rate,
                        start_views,
                        end_views,
                        view_change,
                        view_growth_rate,
                        start_revenue,
                        end_revenue,
                        revenue_change,
                        revenue_growth_rate,
                        existing_record['id']
                    ))
                else:
                    # 插入新记录
                    sql = """
                        INSERT INTO ytb_channel_time_statistics (
                            channel_id, period_type, period_start, period_end,
                            start_subscribers, end_subscribers, subscriber_change, subscriber_growth_rate,
                            start_views, end_views, view_change, view_growth_rate,
                            start_revenue, end_revenue, revenue_change, revenue_growth_rate,
                            year, quarter, month, week, day,
                            created_at, updated_at
                        ) VALUES (
                            %s, %s, %s, %s,
                            %s, %s, %s, %s,
                            %s, %s, %s, %s,
                            %s, %s, %s, %s,
                            %s, %s, %s, %s, %s,
                            NOW(), NOW()
                        ) RETURNING id
                    """
                    
                    cursor.execute(sql, (
                        channel_id,
                        period_type,
                        period_start.date(),
                        period_end.date(),
                        start_subscribers,
                        end_subscribers,
                        subscriber_change,
                        subscriber_growth_rate,
                        start_views,
                        end_views,
                        view_change,
                        view_growth_rate,
                        start_revenue,
                        end_revenue,
                        revenue_change,
                        revenue_growth_rate,
                        year,
                        quarter,
                        month,
                        week,
                        day
                    ))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"保存频道时间统计数据失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                db_pool.close_connection(conn, cursor)
    
    @staticmethod
    def get_revenue_data(channel_id: str, start_date: datetime, end_date: datetime) -> Tuple[float, float]:
        """获取特定频道在给定日期范围内的收益数据
        
        Args:
            channel_id: 频道ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Tuple[float, float]: (开始收益, 结束收益)
        """
        # 直接返回0,0，跳过收益数据计算
        return 0, 0
        
        # 原实现被注释掉
        """
        conn = None
        cursor = None
        try:
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 查询开始日期的收益
            start_sql = '''
            SELECT SUM(estimated_revenue) as revenue
            FROM ytb_channel_analytics
            WHERE authorized_channel_id = %s
            AND data_date = %s::date
            AND deleted_at IS NULL
            '''
            
            cursor.execute(start_sql, (channel_id, start_date.date()))
            start_result = cursor.fetchone()
            start_revenue = start_result['revenue'] if start_result and start_result['revenue'] else 0
            
            # 查询结束日期的收益
            end_sql = '''
            SELECT SUM(estimated_revenue) as revenue
            FROM ytb_channel_analytics
            WHERE authorized_channel_id = %s
            AND data_date = %s::date
            AND deleted_at IS NULL
            '''
            
            cursor.execute(end_sql, (channel_id, end_date.date()))
            end_result = cursor.fetchone()
            end_revenue = end_result['revenue'] if end_result and end_result['revenue'] else 0
            
            return start_revenue, end_revenue
            
        except Exception as e:
            logger.error(f"获取频道收益数据失败: {str(e)}")
            return 0, 0
        finally:
            if conn and cursor:
                db_pool.close_connection(conn, cursor)
        """
    
    @staticmethod
    def process_daily_statistics(channel_id: str, date: datetime) -> bool:
        """处理日度统计数据
        
        计算方式：使用当天的最新快照数据与前一天的最早快照数据进行对比，
        计算订阅量、观看量的绝对变化量和增长率(百分比)。
        
        Args:
            channel_id (str): 频道ID
            date (datetime): 日期
            
        Returns:
            bool: 操作是否成功
        """
        # 获取当天的快照
        today_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 获取昨天的日期范围
        yesterday = date - timedelta(days=1)
        yesterday_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 获取今天和昨天的快照
        today_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, today_start, today_end)
        yesterday_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, yesterday_start, yesterday_end)
        
        if not today_snapshots or not yesterday_snapshots:
            logger.warning(f"频道 {channel_id} 在 {date.date()} 或前一天没有足够的快照数据")
            return False
        
        # 使用今天的最新快照和昨天的最早快照进行对比
        today_snapshot = today_snapshots[-1]  # 今天的最新数据
        yesterday_snapshot = yesterday_snapshots[0]  # 昨天的最早数据
        
        # 默认设置收益为0
        start_revenue, end_revenue = 0, 0
        
        # 计算绝对增长量
        subscriber_change = today_snapshot['subscriber_count'] - yesterday_snapshot['subscriber_count']
        view_change = today_snapshot['view_count'] - yesterday_snapshot['view_count']
        
        # 计算环比增长率
        subscriber_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            yesterday_snapshot['subscriber_count'], today_snapshot['subscriber_count'])
        view_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            yesterday_snapshot['view_count'], today_snapshot['view_count'])
        
        logger.info(f"频道 {channel_id} 日度统计 ({date.date()})："
                   f"订阅: {yesterday_snapshot['subscriber_count']} -> {today_snapshot['subscriber_count']} (变化: {subscriber_change}, 增长率: {subscriber_growth_rate:.2f}%), "
                   f"观看: {yesterday_snapshot['view_count']} -> {today_snapshot['view_count']} (变化: {view_change}, 增长率: {view_growth_rate:.2f}%), "
                   f"收益: {start_revenue} -> {end_revenue}")
        
        # 保存日度统计
        return ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="day",
            period_start=yesterday_start,
            period_end=today_end,
            start_subscribers=yesterday_snapshot['subscriber_count'],
            end_subscribers=today_snapshot['subscriber_count'],
            start_views=yesterday_snapshot['view_count'],
            end_views=today_snapshot['view_count'],
            start_revenue=start_revenue,
            end_revenue=end_revenue,
            year=date.year,
            month=date.month,
            day=date.day
        )
    
    @staticmethod
    def process_weekly_statistics(channel_id: str, date: datetime) -> bool:
        """处理周度统计数据
        
        计算方式：使用本周的最新快照数据与上周的最早快照数据进行对比，
        计算订阅量、观看量的绝对变化量和增长率(百分比)。
        
        Args:
            channel_id (str): 频道ID
            date (datetime): 日期
            
        Returns:
            bool: 操作是否成功
        """
        # 计算本周的开始和结束时间
        current_weekday = date.weekday()
        week_start = date - timedelta(days=current_weekday)
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
        week_end = date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 计算上周的开始和结束时间
        last_week_start = week_start - timedelta(days=7)
        last_week_end = week_start - timedelta(microseconds=1)
        
        # 获取本周和上周的快照
        current_week_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, week_start, week_end)
        last_week_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, last_week_start, last_week_end)
        
        if not current_week_snapshots or not last_week_snapshots:
            logger.warning(f"频道 {channel_id} 在 {date.date()} 所在周或上周没有足够的快照数据")
            return False
        
        # 使用本周的最新快照和上周的最早快照
        current_week_snapshot = current_week_snapshots[-1]
        last_week_snapshot = last_week_snapshots[0]
        
        # 默认设置收益为0
        start_revenue, end_revenue = 0, 0
        
        # 计算绝对增长量
        subscriber_change = current_week_snapshot['subscriber_count'] - last_week_snapshot['subscriber_count']
        view_change = current_week_snapshot['view_count'] - last_week_snapshot['view_count']
        
        # 计算环比增长率
        subscriber_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            last_week_snapshot['subscriber_count'], current_week_snapshot['subscriber_count'])
        view_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            last_week_snapshot['view_count'], current_week_snapshot['view_count'])
        
        # 获取年和周数
        year, week_number, _ = date.isocalendar()
        
        logger.info(f"频道 {channel_id} 周度统计 ({year}年第{week_number}周)："
                   f"订阅: {last_week_snapshot['subscriber_count']} -> {current_week_snapshot['subscriber_count']} (变化: {subscriber_change}, 增长率: {subscriber_growth_rate:.2f}%), "
                   f"观看: {last_week_snapshot['view_count']} -> {current_week_snapshot['view_count']} (变化: {view_change}, 增长率: {view_growth_rate:.2f}%), "
                   f"收益: {start_revenue} -> {end_revenue}")
        
        # 保存周度统计
        return ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="week",
            period_start=last_week_start,
            period_end=week_end,
            start_subscribers=last_week_snapshot['subscriber_count'],
            end_subscribers=current_week_snapshot['subscriber_count'],
            start_views=last_week_snapshot['view_count'],
            end_views=current_week_snapshot['view_count'],
            start_revenue=start_revenue,
            end_revenue=end_revenue,
            year=year,
            week=week_number
        )
    
    @staticmethod
    def process_monthly_statistics(channel_id: str, date: datetime) -> bool:
        """处理月度统计数据
        
        计算方式：使用本月的最新快照数据与上月的最早快照数据进行对比，
        计算订阅量、观看量的绝对变化量和增长率(百分比)。
        
        Args:
            channel_id (str): 频道ID
            date (datetime): 日期
            
        Returns:
            bool: 操作是否成功
        """
        # 计算本月的开始和结束时间
        current_month_start = date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        current_month_end = date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 计算上月的开始和结束时间
        if date.month == 1:
            last_month_start = date.replace(year=date.year-1, month=12, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            last_month_start = date.replace(month=date.month-1, day=1, hour=0, minute=0, second=0, microsecond=0)
        
        last_month_end = current_month_start - timedelta(microseconds=1)
        
        # 获取本月和上月的快照
        current_month_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, current_month_start, current_month_end)
        last_month_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, last_month_start, last_month_end)
        
        if not current_month_snapshots or not last_month_snapshots:
            logger.warning(f"频道 {channel_id} 在 {date.year}年{date.month}月或上月没有足够的快照数据")
            return False
        
        # 使用本月的最新快照和上月的最早快照
        current_month_snapshot = current_month_snapshots[-1]
        last_month_snapshot = last_month_snapshots[0]
        
        # 默认设置收益为0
        start_revenue, end_revenue = 0, 0
        
        # 计算绝对增长量
        subscriber_change = current_month_snapshot['subscriber_count'] - last_month_snapshot['subscriber_count']
        view_change = current_month_snapshot['view_count'] - last_month_snapshot['view_count']
        
        # 计算环比增长率
        subscriber_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            last_month_snapshot['subscriber_count'], current_month_snapshot['subscriber_count'])
        view_growth_rate = ChannelStatsUpdater.calculate_growth_rate(
            last_month_snapshot['view_count'], current_month_snapshot['view_count'])
        
        logger.info(f"频道 {channel_id} 月度统计 ({date.year}年{date.month}月)："
                   f"订阅: {last_month_snapshot['subscriber_count']} -> {current_month_snapshot['subscriber_count']} (变化: {subscriber_change}, 增长率: {subscriber_growth_rate:.2f}%), "
                   f"观看: {last_month_snapshot['view_count']} -> {current_month_snapshot['view_count']} (变化: {view_change}, 增长率: {view_growth_rate:.2f}%), "
                   f"收益: {start_revenue} -> {end_revenue}")
        
        # 保存月度统计
        return ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="month",
            period_start=last_month_start,
            period_end=current_month_end,
            start_subscribers=last_month_snapshot['subscriber_count'],
            end_subscribers=current_month_snapshot['subscriber_count'],
            start_views=last_month_snapshot['view_count'],
            end_views=current_month_snapshot['view_count'],
            start_revenue=start_revenue,
            end_revenue=end_revenue,
            year=date.year,
            month=date.month
        )
    
    @staticmethod
    def process_quarterly_statistics(channel_id: str, date: datetime) -> bool:
        """处理季度统计数据
        
        Args:
            channel_id (str): 频道ID
            date (datetime): 当前日期
            
        Returns:
            bool: 操作是否成功
        """
        # 计算当前季度
        year = date.year
        month = date.month
        quarter = (month - 1) // 3 + 1
        
        # 获取季度的开始和结束月份
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        
        # 获取季度开始和结束的日期
        start_date = date.replace(month=start_month, day=1)
        
        # 获取下个季度第一天，然后减去一天得到当季度最后一天
        if end_month == 12:
            end_date = date.replace(year=year+1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = date.replace(month=end_month+1, day=1) - timedelta(days=1)
        
        # 获取季度开始和结束的快照
        start_time = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        start_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, start_time, start_time + timedelta(days=1))
        end_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, end_time - timedelta(days=1), end_time)
        
        if not start_snapshots or not end_snapshots:
            logger.warning(f"频道 {channel_id} 在季度 {year}Q{quarter} 没有足够的快照数据")
            return False
        
        # 使用最早和最晚的快照
        start_snapshot = start_snapshots[0]
        end_snapshot = end_snapshots[-1]
        
        # 默认设置收益为0
        start_revenue, end_revenue = 0, 0
        
        # 保存季度统计
        return ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="quarter",
            period_start=start_time,
            period_end=end_time,
            start_subscribers=start_snapshot['subscriber_count'],
            end_subscribers=end_snapshot['subscriber_count'],
            start_views=start_snapshot['view_count'],
            end_views=end_snapshot['view_count'],
            start_revenue=start_revenue,
            end_revenue=end_revenue,
            year=year,
            quarter=quarter
        )
    
    @staticmethod
    def process_yearly_statistics(channel_id: str, date: datetime) -> bool:
        """处理年度统计数据
        
        Args:
            channel_id (str): 频道ID
            date (datetime): 当前日期
            
        Returns:
            bool: 操作是否成功
        """
        # 计算当前年份
        year = date.year
        
        # 获取年度的开始和结束日期
        start_date = date.replace(month=1, day=1)
        end_date = date.replace(month=12, day=31)
        
        # 获取年度开始和结束的快照
        start_time = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        start_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, start_time, start_time + timedelta(days=1))
        end_snapshots = ChannelStatsUpdater.get_channel_snapshots(channel_id, end_time - timedelta(days=1), end_time)
        
        if not start_snapshots or not end_snapshots:
            logger.warning(f"频道 {channel_id} 在年度 {year} 没有足够的快照数据")
            return False
        
        # 使用最早和最晚的快照
        start_snapshot = start_snapshots[0]
        end_snapshot = end_snapshots[-1]
        
        # 默认设置收益为0
        start_revenue, end_revenue = 0, 0
        
        # 保存年度统计
        return ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="year",
            period_start=start_time,
            period_end=end_time,
            start_subscribers=start_snapshot['subscriber_count'],
            end_subscribers=end_snapshot['subscriber_count'],
            start_views=start_snapshot['view_count'],
            end_views=end_snapshot['view_count'],
            start_revenue=start_revenue,
            end_revenue=end_revenue,
            year=year
        )
    
    @staticmethod
    def update_channel_statistics(channel_id: str, target_date: Optional[datetime] = None) -> bool:
        """更新指定频道的所有统计数据
        
        Args:
            channel_id (str): 频道ID
            target_date (datetime, optional): 目标日期，默认为昨天
            
        Returns:
            bool: 操作是否成功
        """
        if target_date is None:
            # 默认处理昨天的数据
            target_date = datetime.now() - timedelta(days=1)
        
        logger.info(f"开始处理频道 {channel_id} 在 {target_date.date()} 的统计数据")
        
        # 处理各个时间段的统计
        daily_success = ChannelStatsUpdater.process_daily_statistics(channel_id, target_date)
        weekly_success = ChannelStatsUpdater.process_weekly_statistics(channel_id, target_date)
        monthly_success = ChannelStatsUpdater.process_monthly_statistics(channel_id, target_date)
        quarterly_success = ChannelStatsUpdater.process_quarterly_statistics(channel_id, target_date)
        yearly_success = ChannelStatsUpdater.process_yearly_statistics(channel_id, target_date)
        
        logger.info(f"频道 {channel_id} 统计数据处理完成: "
                  f"日={daily_success}, 周={weekly_success}, 月={monthly_success}, "
                  f"季={quarterly_success}, 年={yearly_success}")
        
        # 如果至少有一个时间段成功处理，则认为更新成功
        return any([daily_success, weekly_success, monthly_success, quarterly_success, yearly_success])
    
    @staticmethod
    def update_all_channel_statistics(target_date: Optional[datetime] = None) -> bool:
        """更新所有频道的统计数据
        
        Args:
            target_date (datetime, optional): 目标日期，默认为昨天
            
        Returns:
            bool: 操作是否成功
        """
        if target_date is None:
            # 默认处理昨天的数据
            target_date = datetime.now() - timedelta(days=1)
        
        logger.info(f"开始更新所有频道在 {target_date.date()} 的统计数据")
        
        # 获取所有频道
        channels = ChannelStatsUpdater.get_channels()
        
        if not channels:
            logger.warning("没有找到频道数据")
            return False
        
        success_count = 0
        total_count = len(channels)
        
        # 处理每个频道
        for channel in channels:
            channel_id = channel['channel_id']
            channel_title = channel.get('title', '未知频道')
            # 临时测试数据
            # if channel_id != 'UCQOt3FI6FBeYecatZq7auyA':
            #     continue
            try:
                success = ChannelStatsUpdater.update_channel_statistics(channel_id, target_date)
                if success:
                    success_count += 1
                    logger.info(f"成功更新频道 '{channel_title}' ({channel_id}) 的统计数据")
                else:
                    logger.warning(f"无法更新频道 '{channel_title}' ({channel_id}) 的统计数据")
            except Exception as e:
                logger.error(f"处理频道 '{channel_title}' ({channel_id}) 时出错: {str(e)}")
        
        logger.info(f"统计数据更新完成: 成功 {success_count}/{total_count} 个频道")
        
        return success_count > 0

    @staticmethod
    def ensure_database_structure() -> bool:
        """确保所需的数据库表和索引已创建
        
        Returns:
            bool: 操作是否成功
        """
        conn = None
        cursor = None
        try:
            conn = db_pool.get_connection()
            cursor = conn.cursor(cursor_factory=db_pool.get_dict_cursor)
            
            # 首先检查快照表的结构
            snapshot_check_sql = """
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'ytb_channel_stats_snapshots' AND column_name = 'channel_id';
            """
            
            cursor.execute(snapshot_check_sql)
            snapshot_info = cursor.fetchone()
            
            # 检查表是否存在
            if snapshot_info is None:
                logger.info("创建频道时间统计表，因为快照表不存在或结构不完整")
                # 直接创建频道时间统计表
                create_table_sql = """
                    CREATE TABLE IF NOT EXISTS ytb_channel_time_statistics (
                        id                      BIGSERIAL       PRIMARY KEY,
                        channel_id              VARCHAR(50)     NOT NULL REFERENCES ytb_channels(channel_id), -- 关联频道ID字符串
                        period_type             VARCHAR(20)     NOT NULL,                  -- 统计周期类型(day/week/month/quarter/year)
                        period_start            DATE            NOT NULL,                  -- 统计周期开始日期
                        period_end              DATE            NOT NULL,                  -- 统计周期结束日期
                        
                        -- 订阅数据
                        start_subscribers       INTEGER         NOT NULL DEFAULT 0,        -- 周期开始订阅数
                        end_subscribers         INTEGER         NOT NULL DEFAULT 0,        -- 周期结束订阅数
                        subscriber_change       INTEGER         NOT NULL DEFAULT 0,        -- 订阅数变化
                        subscriber_growth_rate  NUMERIC(6,2),                            -- 订阅增长率(%)
                        
                        -- 观看数据
                        start_views             BIGINT          NOT NULL DEFAULT 0,        -- 周期开始观看数
                        end_views               BIGINT          NOT NULL DEFAULT 0,        -- 周期结束观看数
                        view_change             BIGINT          NOT NULL DEFAULT 0,        -- 观看数变化
                        view_growth_rate        NUMERIC(6,2),                            -- 观看增长率(%)
                        
                        -- 收益数据
                        start_revenue           NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期开始收益
                        end_revenue             NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期结束收益
                        revenue_change          NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 收益变化
                        revenue_growth_rate     NUMERIC(6,2),                            -- 收益增长率(%)
                        
                        -- 时间维度
                        year                    INTEGER,                                  -- 年份
                        quarter                 INTEGER,                                  -- 季度(1-4)
                        month                   INTEGER,                                  -- 月份(1-12)
                        week                    INTEGER,                                  -- 周数(1-53)
                        day                     INTEGER,                                  -- 日期(1-31)
                        
                        created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
                        updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
                        deleted_at              TIMESTAMPTZ                                 -- 记录软删除时间
                    );
                    
                    -- 确保有唯一约束
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_channel_time_stats_unique 
                    ON ytb_channel_time_statistics(channel_id, period_type, period_start, period_end) 
                    WHERE deleted_at IS NULL;
                    
                    -- 创建其他索引
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_channel_id 
                    ON ytb_channel_time_statistics(channel_id);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_period 
                    ON ytb_channel_time_statistics(period_type, period_start, period_end);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_date_parts 
                    ON ytb_channel_time_statistics(year, quarter, month, week, day);
                """
                
                cursor.execute(create_table_sql)
                conn.commit()
                logger.info("频道时间统计表创建成功")
                return True
            
            # 如果快照表中的channel_id是bigint类型，修改为varchar类型
            if snapshot_info and snapshot_info['data_type'] == 'bigint':
                logger.warning("检测到ytb_channel_stats_snapshots表中channel_id是BIGINT类型，将进行修改")
                
                try:
                    # 1. 检查表中是否有数据
                    check_data_sql = "SELECT COUNT(*) FROM ytb_channel_stats_snapshots"
                    cursor.execute(check_data_sql)
                    count_row = cursor.fetchone()
                    count = count_row[0] if count_row else 0
                    
                    # 如果表中有数据，则进行迁移
                    if count > 0:
                        logger.info(f"检测到ytb_channel_stats_snapshots表中有{count}条数据，将进行迁移")
                        
                        # 1. 先创建临时表存储现有数据
                        create_temp_table_sql = """
                        CREATE TEMP TABLE temp_channel_snapshots AS
                        SELECT s.id, c.channel_id, s.view_count, s.subscriber_count, s.video_count, 
                               s.snapshot_at, s.created_at, s.updated_at, s.deleted_at
                        FROM ytb_channel_stats_snapshots s
                        JOIN ytb_channels c ON s.channel_id = c.id;
                        """
                        cursor.execute(create_temp_table_sql)
                        logger.info("临时表创建成功，已复制现有数据")
                        
                        # 2. 删除外键约束
                        logger.info("正在检查并删除外键约束...")
                        constraint_check_sql = """
                        SELECT constraint_name 
                        FROM information_schema.table_constraints 
                        WHERE constraint_type = 'FOREIGN KEY' 
                        AND table_name = 'ytb_channel_stats_snapshots'
                        AND constraint_name LIKE '%channel_id%'
                        """
                        
                        cursor.execute(constraint_check_sql)
                        constraint = cursor.fetchone()
                        
                        if constraint:
                            constraint_name = constraint['constraint_name']
                            drop_constraint_sql = f"ALTER TABLE ytb_channel_stats_snapshots DROP CONSTRAINT {constraint_name}"
                            cursor.execute(drop_constraint_sql)
                            logger.info(f"已删除外键约束: {constraint_name}")
                        else:
                            logger.info("未发现外键约束")
                            
                        # 3. 修改字段类型
                        modify_column_sql = "ALTER TABLE ytb_channel_stats_snapshots ALTER COLUMN channel_id TYPE VARCHAR(50) USING NULL"
                        cursor.execute(modify_column_sql)
                        logger.info("已修改channel_id字段类型为VARCHAR(50)")
                        
                        # 4. 添加外键约束
                        add_constraint_sql = """
                        ALTER TABLE ytb_channel_stats_snapshots
                        ADD CONSTRAINT fk_channel_stats_channel_id 
                        FOREIGN KEY (channel_id) REFERENCES ytb_channels(channel_id)
                        """
                        cursor.execute(add_constraint_sql)
                        logger.info("已添加新的外键约束")
                        
                        # 5. 清空表并恢复数据
                        truncate_sql = "TRUNCATE ytb_channel_stats_snapshots"
                        cursor.execute(truncate_sql)
                        logger.info("已清空ytb_channel_stats_snapshots表")
                        
                        # 恢复数据
                        restore_data_sql = """
                        INSERT INTO ytb_channel_stats_snapshots (
                            id, channel_id, view_count, subscriber_count, video_count,
                            snapshot_at, created_at, updated_at, deleted_at
                        )
                        SELECT id, channel_id, view_count, subscriber_count, video_count,
                               snapshot_at, created_at, updated_at, deleted_at
                        FROM temp_channel_snapshots
                        """
                        cursor.execute(restore_data_sql)
                        logger.info("已从临时表恢复数据")
                        
                        # 6. 重置序列
                        reset_sequence_sql = """
                        SELECT setval(pg_get_serial_sequence('ytb_channel_stats_snapshots', 'id'), 
                               (SELECT COALESCE(MAX(id), 0) FROM ytb_channel_stats_snapshots) + 1)
                        """
                        cursor.execute(reset_sequence_sql)
                        logger.info("已重置序列")
                    else:
                        # 如果表中没有数据，直接修改结构
                        logger.info("表中没有数据，将直接修改结构")
                        
                        # 删除外键约束（如果有）
                        constraint_check_sql = """
                        SELECT constraint_name 
                        FROM information_schema.table_constraints 
                        WHERE constraint_type = 'FOREIGN KEY' 
                        AND table_name = 'ytb_channel_stats_snapshots'
                        AND constraint_name LIKE '%channel_id%'
                        """
                        
                        cursor.execute(constraint_check_sql)
                        constraint = cursor.fetchone()
                        
                        if constraint:
                            constraint_name = constraint['constraint_name']
                            drop_constraint_sql = f"ALTER TABLE ytb_channel_stats_snapshots DROP CONSTRAINT {constraint_name}"
                            cursor.execute(drop_constraint_sql)
                            logger.info(f"已删除外键约束: {constraint_name}")
                        
                        # 修改字段类型
                        modify_column_sql = "ALTER TABLE ytb_channel_stats_snapshots ALTER COLUMN channel_id TYPE VARCHAR(50) USING NULL"
                        cursor.execute(modify_column_sql)
                        logger.info("已修改channel_id字段类型为VARCHAR(50)")
                        
                        # 添加外键约束
                        add_constraint_sql = """
                        ALTER TABLE ytb_channel_stats_snapshots
                        ADD CONSTRAINT fk_channel_stats_channel_id 
                        FOREIGN KEY (channel_id) REFERENCES ytb_channels(channel_id)
                        """
                        cursor.execute(add_constraint_sql)
                        logger.info("已添加新的外键约束")
                    
                    # 7. 重建索引
                    drop_index_sql = "DROP INDEX IF EXISTS idx_ytb_channel_stats_channel_id_snapshot_at"
                    cursor.execute(drop_index_sql)
                    logger.info("已删除旧索引")
                    
                    create_index_sql = """
                    CREATE INDEX idx_ytb_channel_stats_channel_id_snapshot_at 
                    ON ytb_channel_stats_snapshots(channel_id, snapshot_at)
                    """
                    cursor.execute(create_index_sql)
                    logger.info("已创建新索引")
                    
                    conn.commit()
                    logger.info("ytb_channel_stats_snapshots表结构已成功更新")
                
                except Exception as e:
                    if conn:
                        conn.rollback()
                    logger.error(f"修改表结构时出错: {str(e)}")
                    # 即使表结构修改失败，仍然继续创建统计表
                    logger.warning("将继续创建频道时间统计表")
                
                # 创建频道时间统计表
                create_table_sql = """
                    CREATE TABLE IF NOT EXISTS ytb_channel_time_statistics (
                        id                      BIGSERIAL       PRIMARY KEY,
                        channel_id              VARCHAR(50)     NOT NULL REFERENCES ytb_channels(channel_id), -- 关联频道ID字符串
                        period_type             VARCHAR(20)     NOT NULL,                  -- 统计周期类型(day/week/month/quarter/year)
                        period_start            DATE            NOT NULL,                  -- 统计周期开始日期
                        period_end              DATE            NOT NULL,                  -- 统计周期结束日期
                        
                        -- 订阅数据
                        start_subscribers       INTEGER         NOT NULL DEFAULT 0,        -- 周期开始订阅数
                        end_subscribers         INTEGER         NOT NULL DEFAULT 0,        -- 周期结束订阅数
                        subscriber_change       INTEGER         NOT NULL DEFAULT 0,        -- 订阅数变化
                        subscriber_growth_rate  NUMERIC(6,2),                            -- 订阅增长率(%)
                        
                        -- 观看数据
                        start_views             BIGINT          NOT NULL DEFAULT 0,        -- 周期开始观看数
                        end_views               BIGINT          NOT NULL DEFAULT 0,        -- 周期结束观看数
                        view_change             BIGINT          NOT NULL DEFAULT 0,        -- 观看数变化
                        view_growth_rate        NUMERIC(6,2),                            -- 观看增长率(%)
                        
                        -- 收益数据
                        start_revenue           NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期开始收益
                        end_revenue             NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期结束收益
                        revenue_change          NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 收益变化
                        revenue_growth_rate     NUMERIC(6,2),                            -- 收益增长率(%)
                        
                        -- 时间维度
                        year                    INTEGER,                                  -- 年份
                        quarter                 INTEGER,                                  -- 季度(1-4)
                        month                   INTEGER,                                  -- 月份(1-12)
                        week                    INTEGER,                                  -- 周数(1-53)
                        day                     INTEGER,                                  -- 日期(1-31)
                        
                        created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
                        updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
                        deleted_at              TIMESTAMPTZ                                 -- 记录软删除时间
                    );
                    
                    -- 确保有唯一约束
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_channel_time_stats_unique 
                    ON ytb_channel_time_statistics(channel_id, period_type, period_start, period_end) 
                    WHERE deleted_at IS NULL;
                    
                    -- 创建其他索引
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_channel_id 
                    ON ytb_channel_time_statistics(channel_id);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_period 
                    ON ytb_channel_time_statistics(period_type, period_start, period_end);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_date_parts 
                    ON ytb_channel_time_statistics(year, quarter, month, week, day);
                """
                
                cursor.execute(create_table_sql)
                conn.commit()
                logger.info("频道时间统计表创建/更新成功")
                return True
            
            # 如果channel_id已经是varchar类型，只需要创建统计表
            if snapshot_info and snapshot_info['data_type'] == 'character varying':
                logger.info("ytb_channel_stats_snapshots表结构已是最新，channel_id为VARCHAR类型")
                
                # 创建频道时间统计表
                create_table_sql = """
                    CREATE TABLE IF NOT EXISTS ytb_channel_time_statistics (
                        id                      BIGSERIAL       PRIMARY KEY,
                        channel_id              VARCHAR(50)     NOT NULL REFERENCES ytb_channels(channel_id), -- 关联频道ID字符串
                        period_type             VARCHAR(20)     NOT NULL,                  -- 统计周期类型(day/week/month/quarter/year)
                        period_start            DATE            NOT NULL,                  -- 统计周期开始日期
                        period_end              DATE            NOT NULL,                  -- 统计周期结束日期
                        
                        -- 订阅数据
                        start_subscribers       INTEGER         NOT NULL DEFAULT 0,        -- 周期开始订阅数
                        end_subscribers         INTEGER         NOT NULL DEFAULT 0,        -- 周期结束订阅数
                        subscriber_change       INTEGER         NOT NULL DEFAULT 0,        -- 订阅数变化
                        subscriber_growth_rate  NUMERIC(6,2),                            -- 订阅增长率(%)
                        
                        -- 观看数据
                        start_views             BIGINT          NOT NULL DEFAULT 0,        -- 周期开始观看数
                        end_views               BIGINT          NOT NULL DEFAULT 0,        -- 周期结束观看数
                        view_change             BIGINT          NOT NULL DEFAULT 0,        -- 观看数变化
                        view_growth_rate        NUMERIC(6,2),                            -- 观看增长率(%)
                        
                        -- 收益数据
                        start_revenue           NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期开始收益
                        end_revenue             NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 周期结束收益
                        revenue_change          NUMERIC(12,2)   NOT NULL DEFAULT 0,        -- 收益变化
                        revenue_growth_rate     NUMERIC(6,2),                            -- 收益增长率(%)
                        
                        -- 时间维度
                        year                    INTEGER,                                  -- 年份
                        quarter                 INTEGER,                                  -- 季度(1-4)
                        month                   INTEGER,                                  -- 月份(1-12)
                        week                    INTEGER,                                  -- 周数(1-53)
                        day                     INTEGER,                                  -- 日期(1-31)
                        
                        created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
                        updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
                        deleted_at              TIMESTAMPTZ                                 -- 记录软删除时间
                    );
                    
                    -- 确保有唯一约束
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_channel_time_stats_unique 
                    ON ytb_channel_time_statistics(channel_id, period_type, period_start, period_end) 
                    WHERE deleted_at IS NULL;
                    
                    -- 创建其他索引
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_channel_id 
                    ON ytb_channel_time_statistics(channel_id);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_period 
                    ON ytb_channel_time_statistics(period_type, period_start, period_end);
                    
                    CREATE INDEX IF NOT EXISTS idx_channel_time_stats_date_parts 
                    ON ytb_channel_time_statistics(year, quarter, month, week, day);
                """
                
                cursor.execute(create_table_sql)
                conn.commit()
                logger.info("频道时间统计表创建/更新成功")
                return True
            
        except Exception as e:
            logger.error(f"创建/更新数据库结构失败: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn and cursor:
                db_pool.close_connection(conn, cursor)

def main():
    """主函数，用于命令行调用"""
    import argparse
    
    # 确保数据库结构正确
    ChannelStatsUpdater.ensure_database_structure()
    
    parser = argparse.ArgumentParser(description='更新YouTube频道统计数据')
    parser.add_argument('--date', type=str, help='处理特定日期的数据 (YYYY-MM-DD格式)，默认为昨天')
    parser.add_argument('--channel-id', type=str, help='只处理指定频道ID的数据')
    args = parser.parse_args()
    
    # 设置目标日期
    target_date = None
    if args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d')
        except ValueError:
            logger.error(f"日期格式无效: {args.date}，应为YYYY-MM-DD格式")
            return False
    
    # 处理单个频道或所有频道
    if args.channel_id:
        return ChannelStatsUpdater.update_channel_statistics(args.channel_id, target_date)
    else:
        return ChannelStatsUpdater.update_all_channel_statistics(target_date)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 