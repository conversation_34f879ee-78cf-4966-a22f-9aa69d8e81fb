import re
import os
import requests
from fastapi import HTTPException
from typing import Optional
from src.dao.channel_dao import ChannelDao
from src.services.channel_service import get_channel_info


# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")
PROXY_HOST = os.getenv("PROXY_HOST", "127.0.0.1")
PROXY_PORT = os.getenv("PROXY_PORT", "7890")
USE_PROXY = os.getenv("USE_PROXY", "false").lower() == "true"
# 添加请求超时时间
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "10"))

if USE_PROXY:
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
else:
    proxies = None



def extract_video_id_from_url(video_url_or_id: str) -> str:
    """从视频URL或ID中提取视频ID"""
    # 如果已经是一个简单的视频ID（11个字符的字母数字），直接返回
    if re.match(r'^[a-zA-Z0-9_-]{11}$', video_url_or_id):
        return video_url_or_id
    
    # 尝试从URL中提取视频ID
    youtube_regex = (
        r'(?:youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=)|youtu\.be\/|youtube.com\/shorts\/)([^&?\n]+)'
    )
    match = re.search(youtube_regex, video_url_or_id)
    if match:
        return match.group(1)
    
    raise ValueError("无法从提供的URL或ID中提取视频ID")

def extract_channel_id(channel_url_or_id: str) -> Optional[str]:
    """从频道URL或ID中提取频道ID"""
    # 如果已经是一个频道ID（以UC开头），直接返回
    if re.match(r'^UC[a-zA-Z0-9_-]+$', channel_url_or_id):
        return channel_url_or_id
    
    # 检查是否是频道URL
    channel_regex = r'(?:youtube\.com\/channel\/)([^\/\n]+)'
    match = re.search(channel_regex, channel_url_or_id)
    if match:
        return match.group(1)
    
    # 检查是否是自定义URL (@username)
    custom_url_regex = r'(?:youtube\.com\/@)([^\/\n]+)'
    match = re.search(custom_url_regex, channel_url_or_id)
    if match:
        # 需要通过API查询获取真实频道ID
        username = match.group(1)
        channel = ChannelDao.get_channel_by_custom_url(f'@{username}')
        if channel:
            return channel["channel_id"]
        else:
            return search_channel_by_username(username)
        # return get_channel_id_from_username(username)
    
    # 检查是否直接使用@username格式（不含URL）
    if channel_url_or_id.startswith('@'):
        # 去掉@前缀，获取用户名
        username = channel_url_or_id[1:]
        channel = ChannelDao.get_channel_by_custom_url(f'@{username}')
        if channel:
            return channel["channel_id"]
        else:
            return search_channel_by_username(username)
        # return get_channel_id_from_username(username)
    
    # 检查是否是频道ID 不带@格式
    channel = ChannelDao.get_channel_by_custom_url(f'@{channel_url_or_id}')
    if channel:
        return channel["channel_id"]
    else:
        return search_channel_by_username(f'@{channel_url_or_id}')
    
    return None

def get_channel_id_from_username(username: str) -> Optional[str]:
    """从用户名获取频道ID"""
    url = "https://www.googleapis.com/youtube/v3/channels"
    params = {
        "key": YOUTUBE_API_KEY,
        "forUsername": username,
        "part": "id"
    }
    
    try:
        response = requests.get(url, params=params, proxies=proxies, timeout=REQUEST_TIMEOUT)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"YouTube API请求失败: {response.text}")
        
        data = response.json()
        if not data.get("items"):
            # 如果forUsername搜索不到，尝试搜索
            return search_channel_by_username(username)
        
        return data["items"][0]["id"]
    except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
        # 请求超时或连接错误
        print(f"获取频道ID请求超时或连接错误: {str(e)}")
        return None
    except Exception as e:
        # 其他未预期的异常
        print(f"获取频道ID时发生异常: {str(e)}")
        return None

def search_channel_by_username(username: str) -> Optional[str]:
    """通过搜索查找频道ID"""
    url = "https://www.googleapis.com/youtube/v3/search"
    params = {
        "key": YOUTUBE_API_KEY,
        "q": username,
        "type": "channel",
        "part": "id",
        "maxResults": 1
    }
    
    try:
        response = requests.get(url, params=params, proxies=proxies, timeout=REQUEST_TIMEOUT)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"YouTube API搜索请求失败: {response.text}")
        
        data = response.json()
        if not data.get("items"):
            return None
        
        channel_id = data["items"][0]["id"]["channelId"]
        get_channel_info(channel_id) # 存储频道信息
        return channel_id
    except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
        # 请求超时或连接错误
        print(f"搜索频道请求超时或连接错误: {str(e)}")
        return None
    except Exception as e:
        # 其他未预期的异常
        print(f"搜索频道时发生异常: {str(e)}")
        return None

def format_duration(seconds):
    """
    将秒数转换为PostgreSQL的INTERVAL格式
    例如：3600 -> '1 hour', 3661 -> '1 hour 1 minute 1 second'
    """
    if not seconds:
        return None
        
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    parts = []
    if hours > 0:
        parts.append(f"{hours} hour{'s' if hours > 1 else ''}")
    if minutes > 0:
        parts.append(f"{minutes} minute{'s' if minutes > 1 else ''}")
    if seconds > 0:
        parts.append(f"{seconds} second{'s' if seconds > 1 else ''}")
    
    return ' '.join(parts) if parts else None 