import logging
import logging.config
import yaml
import os
from pathlib import Path
from datetime import datetime

def setup_logging():
    """设置基础日志配置"""
    # 创建日志格式
    log_format = '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 基础配置
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format
    )
    
    # 减少一些库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('schedule').setLevel(logging.WARNING)

def get_logger(name):
    """获取指定名称的日志记录器"""
    logger = logging.getLogger(name)
    
    # 如果已经有处理器，说明已经配置过，直接返回
    if logger.handlers:
        return logger
        
    # 创建文件处理器
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    file_handler = logging.FileHandler(
        log_dir / f'{name}.log',
        encoding='utf-8'
    )
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    
    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 确保日志级别设置正确
    logger.setLevel(logging.INFO)
    
    return logger

def setup_logger(name: str = 'youtube_parser') -> logging.Logger:
    """设置日志记录器"""
    try:
        # 确保日志目录存在
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        # 加载日志配置
        with open('config/logging.yaml', 'r') as f:
            config = yaml.safe_load(f)
            
        logging.config.dictConfig(config)
        return logging.getLogger(name)
        
    except Exception as e:
        # 如果配置加载失败，使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/parser.log', encoding='utf8')
            ]
        )
        logger = logging.getLogger(name)
        logger.error(f"加载日志配置失败: {str(e)}")
        return logger 