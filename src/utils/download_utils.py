import os
import yt_dlp
from .logger import get_logger

# 获取日志记录器
logger = get_logger('download_utils')

# 添加cookie文件路径常量
COOKIES_FILE = '/tmp/yt_cookies.txt'

# 创建临时目录
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "temp_downloads")
os.makedirs(TEMP_DIR, exist_ok=True)

def get_yt_dlp_opts(additional_opts=None):
    """
    获取yt-dlp的基础配置
    """
    opts = {
        'quiet': True,
        'no_warnings': True,
        'sleep_interval': 2,   # 请求间隔2秒
        'max_sleep_interval': 5,  # 最大间隔5秒
        'retries': 10,         # 重试次数
        'fragment_retries': 10, # 片段重试次数
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 尝试加载本地cookie文件
    if os.path.exists(COOKIES_FILE):
        logger.info(f"使用本地cookie文件: {COOKIES_FILE}")
        opts['cookiefile'] = COOKIES_FILE
    else:
        logger.warning(f"本地cookie文件不存在: {COOKIES_FILE}")
    
    # 合并额外的配置选项
    if additional_opts:
        opts.update(additional_opts)
        
    return opts 